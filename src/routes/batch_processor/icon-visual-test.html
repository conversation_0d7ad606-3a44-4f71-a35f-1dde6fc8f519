<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome 视觉检测</title>
    <style>
        @font-face {
            font-family: font-awesome-icon;
            src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
        }
        
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .reference-section {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .ref-examples {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        
        .ref-item {
            text-align: center;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .ref-icon {
            font-family: font-awesome-icon;
            font-size: 32px;
            color: #333;
            display: block;
            margin-bottom: 5px;
        }
        
        .ref-code {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        
        .ref-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-top: 5px;
        }
        
        .good { background: #d4edda; color: #155724; }
        .bad { background: #f8d7da; color: #721c24; }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        .manual-check {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .icon-test {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            border: 3px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
            min-height: 120px;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .icon-test:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .icon {
            font-family: font-awesome-icon;
            font-size: 36px;
            margin-bottom: 10px;
            color: #333;
            line-height: 1;
        }
        
        .icon-code {
            font-size: 14px;
            color: #666;
            font-family: monospace;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        .manual-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 11px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .btn-good { background: #28a745; color: white; }
        .btn-bad { background: #dc3545; color: white; }
        
        .marked-good {
            border-color: #28a745 !important;
            background-color: #d4edda !important;
        }
        
        .marked-bad {
            border-color: #dc3545 !important;
            background-color: #f8d7da !important;
        }
        
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Font Awesome 视觉检测 - 人工确认</h1>
        <p>通过视觉对比来准确识别真实图标和矩形框</p>
        
        <div class="reference-section">
            <strong>参考对比：</strong>
            <div class="ref-examples">
                <div class="ref-item">
                    <span class="ref-icon">&#xf015;</span>
                    <div class="ref-code">f015</div>
                    <div class="ref-status good">真实图标 ✓</div>
                </div>
                <div class="ref-item">
                    <span class="ref-icon">&#xf290;</span>
                    <div class="ref-code">f290</div>
                    <div class="ref-status good">真实图标 ✓</div>
                </div>
                <div class="ref-item">
                    <span class="ref-icon">&#xf0f6;</span>
                    <div class="ref-code">f0f6</div>
                    <div class="ref-status good">真实图标 ✓</div>
                </div>
                <div class="ref-item">
                    <span class="ref-icon">&#xf500;</span>
                    <div class="ref-code">f500</div>
                    <div class="ref-status bad">矩形框 ✗</div>
                </div>
            </div>
        </div>
        
        <div class="manual-check">
            <strong>操作说明：</strong> 点击每个图标下方的"真实图标"或"矩形框"按钮来标记。矩形框通常是带X的方框或空心方框。
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="generateTestSet()">生成测试集</button>
            <button class="btn btn-success" onclick="showMarkedGood()">显示真实图标</button>
            <button class="btn btn-danger" onclick="showMarkedBad()">显示矩形框</button>
            <button class="btn btn-primary" onclick="showAll()">显示全部</button>
            <button class="btn btn-primary" onclick="exportResults()">导出结果</button>
        </div>
        
        <div class="stats" id="stats">
            点击"生成测试集"开始...
        </div>
    </div>
    
    <div class="icon-grid" id="iconGrid"></div>
    
    <script>
        let manualResults = {
            total: 0,
            good: 0,
            bad: 0,
            unmarked: 0,
            goodIcons: [],
            badIcons: [],
            unmarkedIcons: []
        };
        
        function generateTestSet() {
            const grid = document.getElementById('iconGrid');
            grid.innerHTML = '';
            
            // 重点测试范围：f000-f2ff，重点关注可能有问题的区域
            const testCodes = [];
            
            // f0xx 范围采样
            for (let i = 0; i < 256; i += 8) {
                testCodes.push((0xf000 + i).toString(16));
            }
            
            // f1xx 范围采样  
            for (let i = 0; i < 256; i += 8) {
                testCodes.push((0xf100 + i).toString(16));
            }
            
            // f2xx 范围采样
            for (let i = 0; i < 256; i += 8) {
                testCodes.push((0xf200 + i).toString(16));
            }
            
            // 添加一些重要的测试用例
            const knownCases = [
                'f015', 'f007', 'f013', 'f005', 'f080', 'f017', 'f0ad', 'f0f6', // 已知真实图标
                'f500', 'f600', 'f700', 'f800', 'f900', // 高编码，可能是矩形框
                'f290', 'f2a0', 'f280', 'f2b0' // 你刚才测试的混合案例
            ];
            
            knownCases.forEach(code => {
                if (!testCodes.includes(code)) {
                    testCodes.push(code);
                }
            });
            
            // 随机打乱顺序
            testCodes.sort(() => Math.random() - 0.5);
            
            testCodes.forEach(code => {
                const iconDiv = document.createElement('div');
                iconDiv.className = 'icon-test';
                iconDiv.innerHTML = `
                    <span class="icon">&#x${code};</span>
                    <div class="icon-code">${code}</div>
                    <div class="manual-buttons">
                        <button class="btn-small btn-good" onclick="markAsGood('${code}', this)">真实图标</button>
                        <button class="btn-small btn-bad" onclick="markAsBad('${code}', this)">矩形框</button>
                    </div>
                `;
                iconDiv.dataset.code = code;
                iconDiv.dataset.status = 'unmarked';
                grid.appendChild(iconDiv);
            });
            
            manualResults.total = testCodes.length;
            manualResults.unmarked = testCodes.length;
            updateStats();
        }
        
        function markAsGood(code, button) {
            const iconDiv = button.closest('.icon-test');
            const oldStatus = iconDiv.dataset.status;
            
            // 更新状态
            iconDiv.dataset.status = 'good';
            iconDiv.className = 'icon-test marked-good';
            
            // 更新计数
            updateCounts(oldStatus, 'good', code);
            updateStats();
        }
        
        function markAsBad(code, button) {
            const iconDiv = button.closest('.icon-test');
            const oldStatus = iconDiv.dataset.status;
            
            // 更新状态
            iconDiv.dataset.status = 'bad';
            iconDiv.className = 'icon-test marked-bad';
            
            // 更新计数
            updateCounts(oldStatus, 'bad', code);
            updateStats();
        }
        
        function updateCounts(oldStatus, newStatus, code) {
            // 从旧状态中移除
            if (oldStatus === 'good') {
                manualResults.good--;
                manualResults.goodIcons = manualResults.goodIcons.filter(c => c !== code);
            } else if (oldStatus === 'bad') {
                manualResults.bad--;
                manualResults.badIcons = manualResults.badIcons.filter(c => c !== code);
            } else if (oldStatus === 'unmarked') {
                manualResults.unmarked--;
                manualResults.unmarkedIcons = manualResults.unmarkedIcons.filter(c => c !== code);
            }
            
            // 添加到新状态
            if (newStatus === 'good') {
                manualResults.good++;
                manualResults.goodIcons.push(code);
            } else if (newStatus === 'bad') {
                manualResults.bad++;
                manualResults.badIcons.push(code);
            }
        }
        
        function updateStats() {
            const stats = document.getElementById('stats');
            const goodPercent = manualResults.total > 0 ? ((manualResults.good / manualResults.total) * 100).toFixed(1) : 0;
            
            stats.innerHTML = `
                <strong>人工标记进度:</strong> ${manualResults.good + manualResults.bad}/${manualResults.total}<br>
                <strong>✅ 真实图标:</strong> ${manualResults.good} (${goodPercent}%)<br>
                <strong>❌ 矩形框:</strong> ${manualResults.bad}<br>
                <strong>⏳ 未标记:</strong> ${manualResults.unmarked}<br>
                <strong>真实图标示例:</strong> ${manualResults.goodIcons.slice(0, 8).join(', ')}<br>
                <strong>矩形框示例:</strong> ${manualResults.badIcons.slice(0, 8).join(', ')}
            `;
        }
        
        function showMarkedGood() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                if (icon.dataset.status === 'good') {
                    icon.classList.remove('hidden');
                } else {
                    icon.classList.add('hidden');
                }
            });
        }
        
        function showMarkedBad() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                if (icon.dataset.status === 'bad') {
                    icon.classList.remove('hidden');
                } else {
                    icon.classList.add('hidden');
                }
            });
        }
        
        function showAll() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                icon.classList.remove('hidden');
            });
        }
        
        function exportResults() {
            const results = {
                summary: {
                    total: manualResults.total,
                    good: manualResults.good,
                    bad: manualResults.bad,
                    unmarked: manualResults.unmarked,
                    goodPercent: manualResults.total > 0 ? ((manualResults.good / manualResults.total) * 100).toFixed(1) : 0
                },
                goodIcons: manualResults.goodIcons.sort(),
                badIcons: manualResults.badIcons.sort(),
                unmarkedIcons: manualResults.unmarkedIcons.sort(),
                detectionMethod: 'Manual visual inspection',
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'fontawesome-manual-results.json';
            a.click();
            URL.revokeObjectURL(url);
            
            console.log('人工检测结果:', results);
        }
    </script>
</body>
</html>
