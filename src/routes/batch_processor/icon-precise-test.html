<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome 精确检测 - 矩形框识别</title>
    <style>
        @font-face {
            font-family: font-awesome-icon;
            src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
        }
        
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-reference {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .ref-icon {
            font-family: font-awesome-icon;
            font-size: 32px;
            color: #333;
            border: 2px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            background: white;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .icon-test {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
            min-height: 100px;
            justify-content: center;
        }
        
        .icon {
            font-family: font-awesome-icon;
            font-size: 28px;
            margin-bottom: 8px;
            color: #333;
            line-height: 1;
        }
        
        .icon-code {
            font-size: 12px;
            color: #666;
            font-family: monospace;
            margin-bottom: 4px;
        }
        
        .icon-status {
            font-size: 11px;
            padding: 3px 8px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .working {
            background-color: #d4edda !important;
            border-color: #28a745 !important;
        }
        
        .working .icon-status {
            background: #28a745;
            color: white;
        }
        
        .broken {
            background-color: #f8d7da !important;
            border-color: #dc3545 !important;
        }
        
        .broken .icon-status {
            background: #dc3545;
            color: white;
        }
        
        .hidden {
            display: none !important;
        }
        
        .detection-info {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        #canvas-debug {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Font Awesome 精确检测 - 矩形框识别</h1>
        <p>专门检测"缺失字符矩形框"，避免误判</p>
        
        <div class="test-reference">
            <div>
                <strong>已知可用图标:</strong><br>
                <span class="ref-icon">&#xf015;</span> f015 (home)
                <span class="ref-icon">&#xf007;</span> f007 (user)
            </div>
            <div>
                <strong>已知不可用图标（矩形框）:</strong><br>
                <span class="ref-icon">&#xf0f6;</span> f0f6 (file-text-o)
                <span class="ref-icon">&#xf677;</span> f677 (不存在)
            </div>
        </div>
        
        <div class="detection-info">
            <strong>检测原理:</strong> 通过Canvas像素分析，识别矩形框模式。矩形框通常有规律的边框像素分布，而真实图标有更复杂的像素模式。
            <canvas id="canvas-debug" width="64" height="64" style="display: none;"></canvas>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="startPreciseTest()">开始精确检测</button>
            <button class="btn btn-success" onclick="showWorking()">只显示可用</button>
            <button class="btn btn-danger" onclick="showBroken()">只显示矩形框</button>
            <button class="btn btn-primary" onclick="exportResults()">导出结果</button>
            <button class="btn btn-primary" onclick="toggleDebug()">显示调试Canvas</button>
        </div>
        
        <div class="stats" id="stats">
            点击"开始精确检测"开始...
        </div>
    </div>
    
    <div class="icon-grid" id="iconGrid"></div>
    
    <script>
        let testResults = {
            total: 0,
            working: 0,
            broken: 0,
            workingIcons: [],
            brokenIcons: []
        };
        
        let debugMode = false;
        
        function generateTestIcons() {
            const grid = document.getElementById('iconGrid');
            grid.innerHTML = '';
            
            // 测试关键范围和已知图标
            const testCodes = [
                // 已知可用
                'f015', 'f007', 'f013', 'f005', 'f080', 'f017', 'f0ad', 'f0eb', 'f1c0', 'f0e8',
                'f067', 'f068', 'f00c', 'f00d', 'f002', 'f073', 'f07b', 'f15c', 'f0ce', 'f1ad',
                'f055', 'f056', 'f058', 'f057', 'f200', 'f201',
                
                // 已知不可用
                'f0f6', 'f0f7', 'f133', 'f147', 'f10c', 'f099', 'f09a', 'f16d', 'f0e1',
                'f500', 'f600', 'f700', 'f800',
                
                // 边界测试
                'f000', 'f001', 'f002', 'f003', 'f004', 'f005', 'f006', 'f007', 'f008', 'f009',
                'f1f0', 'f1f1', 'f1f2', 'f1f3', 'f1f4', 'f1f5', 'f1f6', 'f1f7', 'f1f8', 'f1f9',
                'f2f0', 'f2f1', 'f2f2', 'f2f3', 'f2f4', 'f2f5', 'f2f6', 'f2f7', 'f2f8', 'f2f9',
                
                // 随机采样
                'f020', 'f030', 'f040', 'f050', 'f060', 'f070', 'f090', 'f0a0', 'f0b0', 'f0c0',
                'f120', 'f130', 'f140', 'f150', 'f160', 'f170', 'f180', 'f190', 'f1a0', 'f1b0',
                'f220', 'f230', 'f240', 'f250', 'f260', 'f270', 'f280', 'f290', 'f2a0', 'f2b0'
            ];
            
            testCodes.forEach(code => {
                const iconDiv = document.createElement('div');
                iconDiv.className = 'icon-test';
                iconDiv.innerHTML = `
                    <span class="icon">&#x${code};</span>
                    <div class="icon-code">${code}</div>
                    <div class="icon-status">待检测</div>
                `;
                iconDiv.dataset.code = code;
                grid.appendChild(iconDiv);
            });
            
            testResults.total = testCodes.length;
        }
        
        function startPreciseTest() {
            generateTestIcons();
            
            setTimeout(() => {
                runPreciseDetection();
            }, 500);
        }
        
        function runPreciseDetection() {
            const icons = document.querySelectorAll('.icon-test');
            testResults.working = 0;
            testResults.broken = 0;
            testResults.workingIcons = [];
            testResults.brokenIcons = [];
            
            // 创建检测canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 48;
            canvas.height = 48;
            
            const debugCanvas = document.getElementById('canvas-debug');
            const debugCtx = debugCanvas.getContext('2d');
            
            icons.forEach((iconDiv, index) => {
                const code = iconDiv.dataset.code;
                const statusEl = iconDiv.querySelector('.icon-status');
                
                // 绘制图标到canvas
                ctx.clearRect(0, 0, 48, 48);
                ctx.font = '32px font-awesome-icon';
                ctx.fillStyle = '#000';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                const char = String.fromCharCode(parseInt(code, 16));
                ctx.fillText(char, 24, 24);
                
                // 获取像素数据
                const imageData = ctx.getImageData(0, 0, 48, 48);
                const pixels = imageData.data;
                
                // 分析像素模式
                const analysis = analyzePixelPattern(pixels, 48, 48);
                
                // 如果是调试模式，显示第一个图标的canvas
                if (debugMode && index === 0) {
                    debugCtx.clearRect(0, 0, 64, 64);
                    debugCtx.drawImage(canvas, 0, 0, 48, 48, 0, 0, 64, 64);
                }
                
                // 判断是否为矩形框
                const isRectangle = detectRectanglePattern(analysis);
                
                if (isRectangle) {
                    markAsBroken(iconDiv, code, statusEl, '矩形框');
                } else if (analysis.hasPixels) {
                    markAsWorking(iconDiv, code, statusEl, '图标');
                } else {
                    markAsBroken(iconDiv, code, statusEl, '空白');
                }
            });
            
            updateStats();
        }
        
        function analyzePixelPattern(pixels, width, height) {
            let totalPixels = 0;
            let edgePixels = 0;
            let centerPixels = 0;
            let corners = [0, 0, 0, 0]; // 四个角的像素数
            
            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const i = (y * width + x) * 4;
                    const alpha = pixels[i + 3];
                    
                    if (alpha > 128) { // 有效像素
                        totalPixels++;
                        
                        // 边缘像素检测
                        if (x === 0 || x === width-1 || y === 0 || y === height-1) {
                            edgePixels++;
                        }
                        
                        // 中心区域像素
                        if (x > width*0.3 && x < width*0.7 && y > height*0.3 && y < height*0.7) {
                            centerPixels++;
                        }
                        
                        // 四个角的像素
                        if (x < width*0.2 && y < height*0.2) corners[0]++;
                        if (x > width*0.8 && y < height*0.2) corners[1]++;
                        if (x < width*0.2 && y > height*0.8) corners[2]++;
                        if (x > width*0.8 && y > height*0.8) corners[3]++;
                    }
                }
            }
            
            return {
                hasPixels: totalPixels > 10,
                totalPixels,
                edgePixels,
                centerPixels,
                corners,
                edgeRatio: totalPixels > 0 ? edgePixels / totalPixels : 0,
                centerRatio: totalPixels > 0 ? centerPixels / totalPixels : 0
            };
        }
        
        function detectRectanglePattern(analysis) {
            if (!analysis.hasPixels) return false;
            
            // 矩形框特征：
            // 1. 边缘像素比例高（矩形边框）
            // 2. 中心像素比例低（空心）
            // 3. 四个角都有像素（矩形角）
            // 4. 总像素数适中（不是实心图标）
            
            const hasHighEdgeRatio = analysis.edgeRatio > 0.6;
            const hasLowCenterRatio = analysis.centerRatio < 0.3;
            const hasAllCorners = analysis.corners.every(c => c > 0);
            const hasModeratePixels = analysis.totalPixels > 50 && analysis.totalPixels < 300;
            
            return hasHighEdgeRatio && hasLowCenterRatio && hasAllCorners && hasModeratePixels;
        }
        
        function markAsWorking(iconDiv, code, statusEl, type) {
            iconDiv.classList.add('working');
            statusEl.textContent = `可用 (${type})`;
            testResults.working++;
            testResults.workingIcons.push(code);
        }
        
        function markAsBroken(iconDiv, code, statusEl, type) {
            iconDiv.classList.add('broken');
            statusEl.textContent = `不可用 (${type})`;
            testResults.broken++;
            testResults.brokenIcons.push(code);
        }
        
        function updateStats() {
            const stats = document.getElementById('stats');
            const workingPercent = ((testResults.working / testResults.total) * 100).toFixed(1);
            
            stats.innerHTML = `
                <strong>精确检测完成!</strong><br>
                <strong>✅ 可用图标:</strong> ${testResults.working} (${workingPercent}%)<br>
                <strong>❌ 不可用图标:</strong> ${testResults.broken}<br>
                <strong>可用图标编码:</strong> ${testResults.workingIcons.slice(0, 10).join(', ')}${testResults.workingIcons.length > 10 ? '...' : ''}<br>
                <strong>不可用图标编码:</strong> ${testResults.brokenIcons.slice(0, 10).join(', ')}${testResults.brokenIcons.length > 10 ? '...' : ''}
            `;
            
            console.log('精确检测结果:', testResults);
        }
        
        function showWorking() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                if (icon.classList.contains('working')) {
                    icon.classList.remove('hidden');
                } else {
                    icon.classList.add('hidden');
                }
            });
        }
        
        function showBroken() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                if (icon.classList.contains('broken')) {
                    icon.classList.remove('hidden');
                } else {
                    icon.classList.add('hidden');
                }
            });
        }
        
        function toggleDebug() {
            debugMode = !debugMode;
            const canvas = document.getElementById('canvas-debug');
            canvas.style.display = debugMode ? 'block' : 'none';
        }
        
        function exportResults() {
            const results = {
                summary: {
                    total: testResults.total,
                    working: testResults.working,
                    broken: testResults.broken,
                    workingPercent: ((testResults.working / testResults.total) * 100).toFixed(1)
                },
                workingIcons: testResults.workingIcons.sort(),
                brokenIcons: testResults.brokenIcons.sort(),
                detectionMethod: 'Rectangle pattern analysis'
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'fontawesome-precise-results.json';
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
