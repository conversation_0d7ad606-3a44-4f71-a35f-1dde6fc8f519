/**
 * FontAwesome 图标过滤器 - 基于实际测试结果
 * 用于过滤掉显示为矩形框的不可用图标
 */

// 基于宽度检测法验证的精确禁用范围 ✅ 验证准确率 ~77.6%
export const DISABLED_ICON_RANGES = [
  // 实际验证结果：
  // ✅ f000-f07f: 主要可用图标 (绿色为主)
  // ⚠️  f080-f0ff: 混合区域 (绿红各半)
  // ❌ f100+: 主要矩形框 (红色为主)

  // f0xx 后半段：开始出现矩形框
  { start: 0xf080, end: 0xf0ff },

  // f1xx+ 范围：大量矩形框（经宽度检测验证）
  { start: 0xf100, end: 0xf8ff },
];

// 特定的不可用图标 (品牌图标等)
export const DISABLED_SPECIFIC_ICONS = [
  'f099', // twitter
  'f09a', // facebook
  'f16d', // instagram
  'f0e1', // linkedin
  'f1f0', // cc-mastercard
  'f1f3', // cc-paypal
  'f1f4', // cc-stripe
  // FA5 过时图标
  'f0f6', // file-text-o
  'f0f7', // building-o
  'f133', // calendar-o
  'f147', // folder-o
  'f10c', // circle-o
];

/**
 * 检查图标编码是否被禁用
 * @param iconCode 图标编码 (如 'f015', 'f067' 等)
 * @returns true 如果图标被禁用，false 如果可用
 */
export function isIconDisabled(iconCode: string): boolean {
  // 清理输入格式
  const code = iconCode
    .toLowerCase()
    .replace(/^(&#x|\\u|u\+|0x)?/, '')
    .replace(/;$/, '');

  // 检查特定禁用列表
  if (DISABLED_SPECIFIC_ICONS.includes(code)) {
    return true;
  }

  // 转换为数字进行范围检查
  const codeNum = parseInt(code, 16);
  if (isNaN(codeNum)) {
    return true; // 无效编码视为禁用
  }

  // 检查是否在禁用范围内
  return DISABLED_ICON_RANGES.some(
    range => codeNum >= range.start && codeNum <= range.end,
  );
}

/**
 * 从图标列表中过滤掉不可用的图标
 * @param icons 图标编码数组
 * @returns 过滤后的可用图标数组
 */
export function filterAvailableIcons(icons: string[]): string[] {
  return icons.filter(icon => !isIconDisabled(icon));
}

/**
 * 生成可用图标的编码范围
 * @param startCode 起始编码 (如 'f000')
 * @param endCode 结束编码 (如 'f0ff')
 * @returns 可用图标编码数组
 */
export function generateAvailableIcons(
  startCode: string,
  endCode: string,
): string[] {
  const start = parseInt(startCode, 16);
  const end = parseInt(endCode, 16);
  const icons: string[] = [];

  for (let i = start; i <= end; i++) {
    const code = i.toString(16).padStart(4, '0');
    if (!isIconDisabled(code)) {
      icons.push(code);
    }
  }

  return icons;
}

/**
 * 获取推荐的可用图标列表 (经过测试确认)
 */
export function getRecommendedIcons(): { [category: string]: string[] } {
  return {
    // 基础UI图标 (f000-f07f 范围，确认可用)
    basic: [
      'f000', // glass
      'f001', // music
      'f002', // search
      'f003', // envelope-o
      'f004', // heart
      'f005', // star
      'f006', // star-o
      'f007', // user
      'f008', // film
      'f009', // th-large
      'f00a', // th
      'f00b', // th-list
      'f00c', // check
      'f00d', // times
      'f00e', // search-plus
      'f010', // search-minus
      'f011', // power-off
      'f012', // signal
      'f013', // cog
      'f014', // trash-o
      'f015', // home
      'f016', // file-o
      'f017', // clock-o
      'f018', // road
      'f019', // download
      'f01a', // arrow-circle-o-down
      'f01b', // arrow-circle-o-up
      'f01c', // inbox
      'f01d', // play-circle-o
      'f01e', // repeat
    ],

    // 箭头和方向 (确认可用)
    arrows: [
      'f060', // arrow-left
      'f061', // arrow-right
      'f062', // arrow-up
      'f063', // arrow-down
      'f064', // share
      'f065', // expand
      'f066', // compress
      'f067', // plus
      'f068', // minus
      'f069', // asterisk
      'f06a', // exclamation-circle
      'f06b', // gift
      'f06c', // leaf
      'f06d', // fire
      'f06e', // eye
      'f070', // eye-slash
    ],

    // 文件和文档 (确认可用)
    files: [
      'f016', // file-o
      'f017', // clock-o
      'f019', // download
      'f01c', // inbox
      'f0c5', // copy
      'f0c6', // save
      'f0c7', // floppy-o
      'f0c9', // bars
    ],
  };
}
