/* 必须的@font-face配置（放在文件顶部） */
@font-face {
  font-family: font-awesome-icon;
  src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
}

/* 页面容器 */
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 2rpx solid #eee;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  display: block;
}

/* 图标网格 */
.icon-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  gap: 20rpx;
}

.icon-item {
  flex: 1;
  min-width: 200rpx;
  max-width: 300rpx;
  padding: 30rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  text-align: center;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 图标样式 */
.icon {
  font-family: font-awesome-icon;
  font-size: 64rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
  line-height: 1;
}

.icon-code {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
  margin-bottom: 8rpx;
  display: block;
}

.icon-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
  font-weight: 500;
}

/* 状态标签 */
.status-good {
  font-size: 22rpx;
  color: #155724;
  background: #d4edda;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: block;
}

.status-warning {
  font-size: 22rpx;
  color: #856404;
  background: #fff3cd;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: block;
}

.status-bad {
  font-size: 22rpx;
  color: #721c24;
  background: #f8d7da;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: block;
}

/* 测试说明 */
.test-info {
  padding: 30rpx;
}

.info-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  margin-bottom: 16rpx;
  display: block;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .icon-grid {
    flex-direction: column;
  }
  
  .icon-item {
    max-width: 100%;
  }
  
  .container {
    padding: 20rpx;
  }
  
  .main-title {
    font-size: 40rpx;
  }
  
  .subtitle {
    font-size: 24rpx;
  }
}
