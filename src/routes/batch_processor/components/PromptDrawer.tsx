import React, { useState, useEffect } from 'react';
import RightDrawer from './RightDrawer';
import Icon, { IconType } from './Icon';
import PromptHistoryList from './PromptHistoryList';
import PromptVersionSelector from './PromptVersionSelector';
import { usePromptHistory } from '../hooks/usePromptHistory';
import {
  PromptTemplateManager,
  PromptHistoryManager,
  PromptTemplate,
} from '../utils/promptTemplateManager';

import { EnhancedBatchProcessorService } from '../services/EnhancedBatchProcessorService';
import { CognitiveOptimizedPrompt } from '../prompts/CognitiveOptimizedPrompt';

// 🚀 三阶段增强系统
import { buildThreeStagePrompt } from '../prompts/ModularPromptLoader';

/**
 * Props interface for PromptDrawer component
 * PromptDrawer 组件的属性接口
 */
interface PromptDrawerProps {
  /** Whether the drawer is visible - 是否显示抽屉 */
  isOpen: boolean;
  /** Callback function to close the drawer - 关闭抽屉回调 */
  onClose: () => void;
  /** Current system prompt content - 当前系统提示词 */
  systemPrompt: string;
  /** Callback function to save prompt - 保存提示词回调 */
  onSave: (prompt: string) => void;
  /** Logger instance for status messages - 日志记录器 */
  logger: {
    success: (message: string) => void;
    error: (message: string) => void;
    info: (message: string) => void;
  };
  /** BatchProcessorService instance - 批处理服务实例 */
  batchProcessorService?: EnhancedBatchProcessorService;
}

/**
 * View modes for the prompt drawer
 * 提示词抽屉的视图模式
 */
enum ViewMode {
  EDITOR = 'editor',
  HISTORY = 'history',
  VERSION_SELECTOR = 'versions',
}

/**
 * Supported template types for prompt templates
 * 支持的提示词模板类型
 */
type TemplateType = 'lynx' | 'web';

/**
 * System prompt editing drawer component
 * 系统提示词编辑抽屉组件
 *
 * Features:
 * - Prompt content editing with syntax highlighting
 * - Template management (Lynx and HTML templates)
 * - History tracking and retrieval
 * - Keyboard shortcuts for quick actions
 * - Auto-save functionality
 *
 * 支持功能:
 * - 提示词内容编辑和语法高亮
 * - 模板管理（Lynx 和 HTML 模板）
 * - 历史记录追踪和检索
 * - 快捷键快速操作
 * - 自动保存功能
 */
const PromptDrawer: React.FC<PromptDrawerProps> = ({
  isOpen,
  onClose,
  systemPrompt,
  onSave,
  logger,
  batchProcessorService,
}) => {
  const [currentPrompt, setCurrentPrompt] = useState(systemPrompt);
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.EDITOR);
  const [isSaving, setIsSaving] = useState(false);

  // 使用提示词历史记录 Hook
  const {
    historyItems,
    isLoading: historyLoading,
    error: historyError,
    addPrompt,
    usePrompt,
    renamePrompt,
    deletePrompt,
    clearHistory,
  } = usePromptHistory();

  /**
   * Synchronize external prompt changes
   * 同步外部提示词变化
   */
  useEffect(() => {
    setCurrentPrompt(systemPrompt);
  }, [systemPrompt]);

  /**
   * Get default template for specified type
   * 获取指定类型的默认模板
   * @param type - Template type (lynx or web)
   * @returns Template content string
   */
  const getDefaultTemplate = (type: TemplateType): string =>
    PromptTemplateManager.getPromptLoaderContent(type);

  /**
   * Handle saving the current prompt
   * 处理保存当前提示词
   * - Saves to external callback
   * - Adds to history if content exists
   * - Closes drawer on success
   */
  const handleSave = async () => {
    try {
      setIsSaving(true);
      // 保存到外部
      onSave(currentPrompt);

      // 添加到历史记录
      if (currentPrompt.trim()) {
        await addPrompt(currentPrompt.trim());
        PromptHistoryManager.addToHistory(currentPrompt.trim());
      }

      // 检查是否使用了HTML模板并提供针对性指引
      const isHTMLTemplate =
        currentPrompt.includes('HTML 移动端图解生成专家') ||
        currentPrompt.includes('知识可视化专家');

      onClose(); // 保存后关闭抽屉

      if (isHTMLTemplate) {
        logger.success(
          '✅ HTML模板已保存！现在可以在主界面输入查询内容（如"制作购物流程图"），然后点击开始处理按钮',
        );
      } else {
        logger.success('系统提示词已保存');
      }
    } catch (error) {
      logger.error(
        `保存失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * Reset to default LYNX template content
   * 重置为默认 LYNX 模板内容
   * Uses the built-in LYNX prompt loader content
   */
  const handleReset = () => {
    const defaultPrompt = PromptTemplateManager.getPromptLoaderContent('lynx');
    setCurrentPrompt(defaultPrompt);
    logger.success('已重置为默认 LYNX 提示词');
  };

  /**
   * Toggle between editor and history view modes
   * 在编辑器和历史记录视图模式之间切换
   */
  const handleToggleView = () => {
    setViewMode(prev => {
      if (prev === ViewMode.EDITOR) return ViewMode.VERSION_SELECTOR;
      if (prev === ViewMode.VERSION_SELECTOR) return ViewMode.HISTORY;
      return ViewMode.EDITOR;
    });
  };

  /**
   * Handle prompt version selection
   * 处理prompt版本选择
   */
  const handlePromptVersionSelect = (content: string, versionName: string) => {
    setCurrentPrompt(content);
    setViewMode(ViewMode.EDITOR);
    logger.success(`${versionName} 已加载到编辑器，请点击"保存提示词"应用`);
  };

  /**
   * Use a prompt from history
   * 使用历史记录中的提示词
   * @param item - History item to use
   */
  const handleUseHistoryPrompt = async (item: {
    id: string;
    content: string;
    title: string;
  }) => {
    try {
      setCurrentPrompt(item.content);
      await usePrompt(item.id);
      setViewMode(ViewMode.EDITOR);
      logger.success(`已切换到提示词: ${item.title}`);
    } catch (error) {
      logger.error(
        `切换提示词失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  };

  /**
   * Handle keyboard shortcuts
   * 处理键盘快捷键
   * - Ctrl+S: Save prompt
   * - Ctrl+R: Reset to default
   * - Ctrl+H: Toggle view mode
   * @param e - Keyboard event
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 's') {
        e.preventDefault();
        handleSave();
      } else if (e.key === 'r') {
        e.preventDefault();
        handleReset();
      } else if (e.key === 'h') {
        e.preventDefault();
        handleToggleView();
      }
    }
  };

  /**
   * Build header action buttons configuration
   * 构建头部操作按钮配置
   */
  const getViewModeIcon = (): IconType => {
    switch (viewMode) {
      case ViewMode.EDITOR:
        return 'layers' as IconType;
      case ViewMode.VERSION_SELECTOR:
        return 'history' as IconType;
      case ViewMode.HISTORY:
        return 'edit' as IconType;
      default:
        return 'edit' as IconType;
    }
  };

  const getViewModeLabel = (): string => {
    switch (viewMode) {
      case ViewMode.EDITOR:
        return '版本';
      case ViewMode.VERSION_SELECTOR:
        return '历史';
      case ViewMode.HISTORY:
        return '编辑';
      default:
        return '编辑';
    }
  };

  const headerActions = [
    {
      icon: getViewModeIcon(),
      label: getViewModeLabel(),
      onClick: handleToggleView,
      className: 'btn-authority btn-secondary-glass tooltip',
    },
  ];

  return (
    <RightDrawer
      isOpen={isOpen}
      onClose={onClose}
      title={
        <h2 className="enhanced-drawer-title">
          <Icon
            type={
              viewMode === ViewMode.EDITOR
                ? 'edit'
                : viewMode === ViewMode.VERSION_SELECTOR
                  ? 'layers'
                  : 'history'
            }
            color="primary"
            size="sm"
            className="flex-shrink-0"
          />
          <span className="flex-1">
            {viewMode === ViewMode.EDITOR
              ? '智能体提示词设定'
              : viewMode === ViewMode.VERSION_SELECTOR
                ? 'Prompt版本选择'
                : '提示词历史记录'}
          </span>
        </h2>
      }
      width="620px"
      theme="prompt"
      headerActions={headerActions}
    >
      {viewMode === ViewMode.HISTORY ? (
        <PromptHistoryList
          items={historyItems}
          isLoading={historyLoading}
          error={historyError}
          onUsePrompt={handleUseHistoryPrompt}
          onRenamePrompt={renamePrompt}
          onDeletePrompt={deletePrompt}
          onClearHistory={clearHistory}
          logger={logger}
        />
      ) : viewMode === ViewMode.VERSION_SELECTOR ? (
        <PromptVersionSelector
          onPromptSelect={handlePromptVersionSelect}
          logger={logger}
        />
      ) : (
        <div className="h-full flex flex-col">
          {/* 使用提示 */}
          <div className="glass-card-gold">
            <h4 className="typography-body-medium flex items-center mb-3">
              <Icon type="info" color="primary" size="sm" className="mr-2" />
              使用提示
            </h4>
            <ul className="typography-body-small space-y-2 text-gray-600">
              <li>• 系统提示词将应用于所有批量处理的查询</li>
              <li>• 默认使用 PE.md 完整开发规范作为初始提示词</li>
              <li>• 用户可以编辑并保存自定义版本</li>
              <li>• 点击"版本"按钮快速切换不同prompt版本</li>
              <li>• 点击"历史"按钮查看和使用历史提示词</li>
              <li>
                • 快捷键: <kbd>Ctrl+S</kbd> 保存, <kbd>Ctrl+R</kbd> 重置,{' '}
                <kbd>Ctrl+H</kbd> 切换视图
              </li>
            </ul>
          </div>

          {/* 编辑区域 */}
          <div className="flex-1 flex flex-col min-h-0">
            <div className="glass-card-gold flex items-center justify-between mb-3">
              <label className="typography-body-large flex items-center gap-2">
                <Icon type="edit" color="primary" size="sm" />
                提示词内容
              </label>
              <div className="flex gap-2">
                <button
                  onClick={() => setCurrentPrompt('')}
                  className="btn-authority btn-secondary-glass text-xs px-3 py-1"
                  title="清空内容"
                >
                  清空
                </button>
                <button
                  onClick={() => {
                    const defaultPrompt =
                      PromptTemplateManager.getPromptLoaderContent('lynx');
                    
                    console.log('🎯 LYNX传统模板按钮点击 - 详细调试信息:');
                    console.log('📊 加载的内容总长度:', defaultPrompt.length);
                    console.log('📄 内容行数:', defaultPrompt.split('\n').length);
                    console.log('🔤 前200字符:', defaultPrompt.substring(0, 200));
                    console.log('🔚 后200字符:', defaultPrompt.substring(defaultPrompt.length - 200));
                    console.log('📋 内容是否包含主要标记:', {
                      hasTitle: defaultPrompt.includes('🎨 Lynx框架世界大师级'),
                      hasComponents: defaultPrompt.includes('组件映射规则'),
                      hasAPI: defaultPrompt.includes('小程序API'),
                      hasEvents: defaultPrompt.includes('事件系统'),
                      hasCanvas: defaultPrompt.includes('Canvas高级绘图')
                    });
                    
                    setCurrentPrompt(defaultPrompt);
                    
                    if (batchProcessorService) {
                      batchProcessorService.setTraditionalMode(true);
                      console.log('✅ 已设置传统模式，禁用RAG处理');
                    }
                    
                    logger.success(
                      `✅ 已加载传统 LYNX 模板！长度: ${defaultPrompt.length} 字符 | 行数: ${defaultPrompt.split('\n').length} | 模式：传统模式 | 请点击"保存提示词"后在主界面输入查询内容并开始批处理`,
                    );
                  }}
                  className="prompt-drawer-button"
                  title="使用传统的完整 LYNX 小程序模板"
                >
                  <Icon
                    type="mobile"
                    color="purple"
                    size="xs"
                    className="mr-1.5"
                  />
                  传统模板
                </button>

                <button
                  onClick={() => {
                    const htmlPrompt =
                      PromptTemplateManager.getPromptLoaderContent('web');
                    setCurrentPrompt(htmlPrompt);
                    logger.success(
                      '✅ 已加载 HTML 模板！请点击"保存提示词"后在主界面输入查询内容并开始批处理',
                    );
                  }}
                  className="prompt-drawer-button"
                  title="使用 HTML 网页生成模板"
                >
                  <Icon
                    type="web"
                    color="purple"
                    size="xs"
                    className="mr-1.5"
                  />
                  HTML模板
                </button>
              </div>
            </div>

            <div className="flex-1 min-h-0">
              <textarea
                value={currentPrompt}
                onChange={e => setCurrentPrompt(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full h-full resize-none border border-gray-200 rounded-lg p-4 font-mono text-sm leading-relaxed focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="在此编辑系统提示词..."
                spellCheck={false}
              />
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="btn-authority btn-primary-glass flex-1 flex items-center justify-center gap-2"
            >
              <Icon
                type={isSaving ? 'loading' : 'save'}
                color="white"
                size="sm"
                className={isSaving ? 'animate-spin' : ''}
              />
              {isSaving ? '保存中...' : '保存提示词'}
            </button>
            <button
              onClick={handleReset}
              className="btn-authority btn-secondary-glass px-6 flex items-center gap-2"
            >
              <Icon type="refresh" color="primary" size="sm" />
              重置
            </button>
          </div>
        </div>
      )}
    </RightDrawer>
  );
};

export default PromptDrawer;
