<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome 图标测试</title>
    <style>
        @font-face {
            font-family: font-awesome-icon;
            src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
        }
        
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .icon-test {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            min-width: 120px;
            background: #fafafa;
        }
        
        .icon {
            font-family: font-awesome-icon;
            font-size: 32px;
            display: block;
            margin-bottom: 8px;
            color: #333;
        }
        
        .icon-code {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        
        .icon-name {
            font-size: 14px;
            color: #333;
            margin-top: 4px;
        }
        
        .status {
            font-size: 12px;
            margin-top: 4px;
            padding: 2px 6px;
            border-radius: 3px;
        }
        
        .working {
            background: #d4edda;
            color: #155724;
        }
        
        .broken {
            background: #f8d7da;
            color: #721c24;
        }
        
        .questionable {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>Font Awesome 6.7.2 图标渲染测试</h1>
    
    <div class="test-container">
        <h2>你的TTML代码中使用的图标</h2>
        
        <div class="icon-test">
            <span class="icon">&#xf080;</span>
            <div class="icon-code">&#xf080;</div>
            <div class="icon-name">chart-bar</div>
            <div class="status working">第一个图标</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf0eb;</span>
            <div class="icon-code">&#xf0eb;</div>
            <div class="icon-name">lightbulb</div>
            <div class="status questionable">第二个图标开始问题</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf1c0;</span>
            <div class="icon-code">&#xf1c0;</div>
            <div class="icon-name">database</div>
            <div class="status questionable">数据库图标</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf0e8;</span>
            <div class="icon-code">&#xf0e8;</div>
            <div class="icon-name">sitemap</div>
            <div class="status questionable">站点地图</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf0f6;</span>
            <div class="icon-code">&#xf0f6;</div>
            <div class="icon-name">file-text-o</div>
            <div class="status broken">FA5过时编码</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf017;</span>
            <div class="icon-code">&#xf017;</div>
            <div class="icon-name">clock</div>
            <div class="status working">时钟图标</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf0ad;</span>
            <div class="icon-code">&#xf0ad;</div>
            <div class="icon-name">wrench</div>
            <div class="status working">工具图标</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf005;</span>
            <div class="icon-code">&#xf005;</div>
            <div class="icon-name">star</div>
            <div class="status working">星星图标</div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>替代方案测试</h2>
        
        <div class="icon-test">
            <span class="icon">&#xf15c;</span>
            <div class="icon-code">&#xf15c;</div>
            <div class="icon-name">file-text</div>
            <div class="status working">替代f0f6</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf0ce;</span>
            <div class="icon-code">&#xf0ce;</div>
            <div class="icon-name">table</div>
            <div class="status working">替代database</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf200;</span>
            <div class="icon-code">&#xf200;</div>
            <div class="icon-name">chart-pie</div>
            <div class="status questionable">饼图替代</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf672;</span>
            <div class="icon-code">&#xf672;</div>
            <div class="icon-name">lightbulb-alt</div>
            <div class="status questionable">灯泡替代</div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>已验证安全的图标</h2>
        
        <div class="icon-test">
            <span class="icon">&#xf015;</span>
            <div class="icon-code">&#xf015;</div>
            <div class="icon-name">home</div>
            <div class="status working">安全</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf013;</span>
            <div class="icon-code">&#xf013;</div>
            <div class="icon-name">cog</div>
            <div class="status working">安全</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf007;</span>
            <div class="icon-code">&#xf007;</div>
            <div class="icon-name">user</div>
            <div class="status working">安全</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf073;</span>
            <div class="icon-code">&#xf073;</div>
            <div class="icon-name">calendar</div>
            <div class="status working">安全</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf07b;</span>
            <div class="icon-code">&#xf07b;</div>
            <div class="icon-name">folder</div>
            <div class="status working">安全</div>
        </div>
        
        <div class="icon-test">
            <span class="icon">&#xf002;</span>
            <div class="icon-code">&#xf002;</div>
            <div class="icon-name">search</div>
            <div class="status working">安全</div>
        </div>
    </div>
    
    <script>
        // 检测图标是否正确渲染
        window.addEventListener('load', function() {
            const icons = document.querySelectorAll('.icon');
            icons.forEach(icon => {
                const rect = icon.getBoundingClientRect();
                const statusEl = icon.parentElement.querySelector('.status');
                
                // 简单检测：如果图标宽度太小，可能没有正确渲染
                if (rect.width < 20) {
                    statusEl.className = 'status broken';
                    statusEl.textContent = '渲染失败';
                }
            });
        });
    </script>
</body>
</html>
