export const TTML_STRICT_CONSTRAINTS = `🎯 TTML STRICT CONSTRAINTS RULES (SOURCE-CODE VERIFIED)

=== R1: FORBIDDEN HTML TAGS (源码: lynx-compiler/TagValidator.ts) ===
RULE: 禁用布局标签 div,section,article,header,footer,span,p,h1-h6,ul,ol,li,table
RULE: 禁用交互标签 button,slider,input[range],select,option,a,label,textarea
RULE: 禁用媒体标签 img,iframe (必须用image,web-view)
RULE: 禁用其他标签 script,style,meta,br,hr,strong,em,b,i

=== R2: CLAUDE4 COMMON ERRORS (源码: lynx-errors/Claude4ErrorTracker.ts) ===
🚨 CRITICAL WARNING: Claude4 频繁违反TTML标签约束，以下规则必须严格执行
RULE: 错误1 button → 必须用 view + bindtap
RULE: 错误2 div → 必须用 view
RULE: 错误3 slider/input[range] → 不存在的组件
RULE: 错误4 span → 必须用 text
RULE: 错误5 canvas + lightcharts-canvas 混用 → 致命错误

❌ 错误示例1 - 使用HTML标签：
WRONG: <div class="container"><button>点击</button></div>
CORRECT: <view class="container"><view bindtap="handleClick" class="button">点击</view></view>

❌ 错误示例2 - 使用HTML文本标签：
WRONG: <h1>标题</h1><p>内容</p>
CORRECT: <text class="title">标题</text><text class="content">内容</text>

=== R3: CANVAS USAGE RULES (源码: lynx-canvas/CanvasValidator.ts) ===
RULE: 绝对禁止在同一页面混用 <canvas> 和 <lightcharts-canvas>
RULE: 方案A 全用原生Canvas <canvas> + lynx.createCanvasNG() + ctx.fillRect()
RULE: 方案B 全用LightChart <lightcharts-canvas> + new LynxChart() + chart.setOption()
RULE: 混用后果 API冲突、渲染失败、运行时错误、应用崩溃

=== R4: REQUIRED TAGS (源码: lynx-components/RequiredTags.ts) ===
RULE: view - 基础容器，替代所有HTML div,section
RULE: text - 文本显示，替代所有HTML span,p,h1-h6
RULE: image - 图片显示，自闭合，替代HTML img
RULE: scroll-view - 可滚动容器，必须设置scroll-x或scroll-y
RULE: input,picker,navigator,web-view,audio,video - 专用组件
RULE: switch,checkbox,radio,progress,icon - 自闭合组件

=== R5: TEXT ESCAPING RULES (源码: lynx-text/TextEscaper.ts) ===
RULE: 特殊字符必须转义 < → &lt;, > → &gt;, & → &amp;, " → &quot;, ' → &apos;
RULE: 数学公式、代码示例、URL参数中的特殊字符必须转义
RULE: 属性值中的引号也需要转义
RULE: 所有TTML标签文字内容必须进行XML实体转义

=== R6: COMPONENT REPLACEMENT RULES (源码: lynx-replacement/ComponentReplacer.ts) ===
RULE: 容器替换 div → view, span → text, img → image
RULE: 交互替换 button → view + bindtap, select → picker, input[range] → slider
RULE: 闭合标签 image,input,progress,icon,checkbox,radio 必须自闭合
RULE: 成对标签 view,text,scroll-view 必须成对闭合

=== R7: SCROLL CONTAINER RULES (源码: lynx-scroll/ScrollContainer.ts) ===
RULE: 卡片最外层必须使用 scroll-view 包裹，禁止 view
RULE: scroll-view 必须设置 height + max-height + scroll-y="true"
RULE: 长列表、卡片堆叠都需要 scroll-view
RULE: 违规后果 内容溢出、布局错乱、用户体验差

=== R8: DATA BINDING RULES (源码: lynx-binding/DataBinder.ts) ===
RULE: 动态数据使用 {{}} 双花括号语法
RULE: 条件渲染使用 tt:if,tt:elif,tt:else
RULE: 列表渲染使用 tt:for,tt:for-item,tt:for-index
RULE: 禁止Vue语法 v-if,v-for 和Angular语法 *ngIf,*ngFor

=== R9: EVENT BINDING RULES (源码: lynx-events/EventBinder.ts) ===
RULE: 点击事件 bindtap="methodName"
RULE: 输入事件 bindinput,bindchange,bindfocus,bindblur
RULE: 滚动事件 bindscroll,bindscrolltoupper,bindscrolltolower
RULE: 禁止HTML/Vue语法 @click,onclick 和Angular语法 (click),(input)

=== R10: LIGHTCHART COMPONENT RULES (源码: lynx-lightchart/LightChartComponent.ts) ===
RULE: 必须使用 <lightcharts-canvas> 标签
RULE: 必须设置 canvasName="uniqueName" + bindinitchart="initMethod"
RULE: 推荐设置 useKrypton="{{SystemInfo.enableKrypton}}"
RULE: canvasName 必须全局唯一，避免冲突

=== R11: TIMELINE COMPONENT STRUCTURE RULES (源码: lynx-timeline/TimelineStructure.ts) ===
🚨 CRITICAL: 时间轴组件结构约束 (防止定位错乱)
RULE: 时间轴容器结构 必须使用统一的定位策略
RULE: 时间轴线条 timeline-line 和节点 timeline-node 定位方式必须一致
RULE: 推荐结构A 使用scroll-view包裹+relative定位+flex布局
RULE: 推荐结构B 使用view容器+absolute定位+精确计算

✅ 正确的时间轴结构A (flex布局方案):
<scroll-view scroll-y="true" class="timeline-container">
  <view class="timeline-wrapper">
    <view class="timeline-line"></view>
    <view class="timeline-node" tt:for="{{items}}" tt:key="id">
      <view class="node-content">{{item.title}}</view>
    </view>
  </view>
</scroll-view>

✅ 正确的时间轴结构B (absolute定位方案):
<view class="timeline-container">
  <view class="timeline-line"></view>
  <view class="timeline-node" tt:for="{{items}}" tt:key="id"
        style="top: {{item.position}}rpx;">
    <view class="node-content">{{item.title}}</view>
  </view>
</view>

=== R12: VALIDATION CHECKLIST (源码: lynx-validator/TTMLValidator.ts) ===
RULE: 检查HTML标签替换 div→view, span→text, img→image
RULE: 检查滚动容器 scroll-view包装+高度设置
RULE: 检查标签闭合 自闭合/>+成对闭合
RULE: 检查Canvas混用 禁止canvas和lightcharts-canvas同时存在
RULE: 检查事件绑定 bindtap,bindinput等Lynx语法
RULE: 检查时间轴结构 timeline组件定位一致性+容器包裹
RULE: 检查数据绑定 {{}}语法+tt:前缀
RULE: 检查特殊字符转义 &lt;&gt;&amp;&quot;&apos;

=== MANDATORY IMPLEMENTATION RULES ===
RULE #1: 标签约束 - 禁用HTML标签+必用Lynx组件+正确闭合
RULE #2: Canvas选择 - 禁止混用+选择统一方案+API匹配
RULE #3: 文字转义 - XML实体转义+特殊字符处理
RULE #4: 组件替换 - 容器交互替换+闭合规则+滚动容器
RULE #5: 数据事件绑定 - Lynx语法+禁用其他框架语法

THESE RULES ARE MANDATORY FOR FUNCTIONAL TTML IMPLEMENTATION
`;
