/**
 * Light Chart 图表绘制修复方案
 * 
 * 主要问题：
 * 1. 数据模型错误 - 混用了坐标系和非坐标系数据格式
 * 2. Canvas初始化时序问题
 * 3. 实例管理不当
 */

export const fixedTrajectoryChart = `
Card({
  data: {
    velocity: 30,
    angle: 45,
    maxHeight: 0,
    range: 0,
    flightTime: 0,
    g: 9.8
  },

  // 修复1：正确的实例存储位置（不在data中）
  trajectoryChart: null,
  velocityChart: null,

  onLoad() {
    this.calculateParameters();
  },

  // 修复2：确保Canvas名称唯一
  initTrajectoryChart(e) {
    const { canvasName, width, height } = e.detail;
    
    // 验证Canvas支持
    if (!tt.canIUse('light-chart')) {
      console.error('设备不支持Light Chart');
      return;
    }
    
    // 验证参数
    if (!canvasName || !width || !height) {
      console.error('Canvas初始化参数不完整:', { canvasName, width, height });
      return;
    }
    
    this.trajectoryChart = new LynxChart({ canvasName, width, height });
    
    // 修复3：延迟初始化防止竞争条件
    setTimeout(() => {
      this.updateTrajectoryChart();
    }, 200);
  },

  initVelocityChart(e) {
    const { canvasName, width, height } = e.detail;
    
    if (!tt.canIUse('light-chart')) {
      console.error('设备不支持Light Chart');
      return;
    }
    
    if (!canvasName || !width || !height) {
      console.error('Canvas初始化参数不完整:', { canvasName, width, height });
      return;
    }
    
    this.velocityChart = new LynxChart({ canvasName, width, height });
    
    setTimeout(() => {
      this.updateVelocityChart();
    }, 200);
  },

  onVelocityChange(e) {
    this.setData({
      velocity: e.detail.value
    });
    this.calculateParameters();
    this.updateCharts();
  },

  onAngleChange(e) {
    this.setData({
      angle: e.detail.value
    });
    this.calculateParameters();
    this.updateCharts();
  },

  // 修复4：统一图表更新方法
  updateCharts() {
    setTimeout(() => {
      this.updateTrajectoryChart();
      this.updateVelocityChart();
    }, 50);
  },

  calculateParameters() {
    const v0 = this.data.velocity;
    const theta = this.data.angle * Math.PI / 180;
    const g = this.data.g;

    const maxHeight = Math.round((v0 * Math.sin(theta)) ** 2 / (2 * g) * 10) / 10;
    const range = Math.round(v0 ** 2 * Math.sin(2 * theta) / g * 10) / 10;
    const flightTime = Math.round(2 * v0 * Math.sin(theta) / g * 100) / 100;

    this.setData({
      maxHeight,
      range,
      flightTime
    });
  },

  // 修复5：正确的数据模型 - 坐标系图表
  updateTrajectoryChart() {
    if (!this.trajectoryChart) return;

    const v0 = this.data.velocity;
    const theta = this.data.angle * Math.PI / 180;
    const g = this.data.g;
    const flightTime = this.data.flightTime;

    // 生成轨迹数据
    const trajectoryData = [];
    const timeStep = flightTime / 100;

    for (let i = 0; i <= 100; i++) {
      const t = i * timeStep;
      const x = v0 * Math.cos(theta) * t;
      const y = v0 * Math.sin(theta) * t - 0.5 * g * t * t;

      if (y >= 0) {
        trajectoryData.push({
          x: Math.round(x * 10) / 10,
          y: Math.round(y * 10) / 10
        });
      }
    }

    // 关键点数据
    const keyPoints = [
      { x: 0, y: 0 },
      { x: this.data.range / 2, y: this.data.maxHeight },
      { x: this.data.range, y: 0 }
    ];

    // 修复6：正确的坐标系图表数据格式
    const option = {
      title: {
        text: \`抛物线轨迹 (v₀=\${v0}m/s, θ=\${this.data.angle}°)\`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: ({c})'
      },
      legend: {
        data: ['轨迹', '关键点'],
        bottom: 10
      },
      grid: {
        left: '12%',
        right: '8%',
        bottom: '15%',
        top: '15%'
      },
      xAxis: {
        type: 'value',
        name: '水平距离 (m)',
        nameLocation: 'center',
        nameGap: 30
      },
      yAxis: {
        type: 'value',
        name: '高度 (m)',
        nameLocation: 'center',
        nameGap: 50
      },
      series: [
        {
          name: '轨迹',
          type: 'line',
          data: trajectoryData.map(point => [point.x, point.y]),
          smooth: true,
          lineStyle: {
            color: '#667eea',
            width: 3
          },
          symbol: 'none'
        },
        {
          name: '关键点',
          type: 'scatter',
          data: keyPoints.map(point => [point.x, point.y]),
          symbolSize: 12,
          itemStyle: {
            color: '#e74c3c'
          }
        }
      ]
    };

    // 修复7：配置验证
    try {
      JSON.stringify(option);
      this.trajectoryChart.setOption(option);
    } catch (e) {
      console.error('轨迹图表配置序列化失败:', e);
    }
  },

  // 修复8：正确的速度图表数据格式
  updateVelocityChart() {
    if (!this.velocityChart) return;

    const v0 = this.data.velocity;
    const theta = this.data.angle * Math.PI / 180;
    const g = this.data.g;
    const flightTime = this.data.flightTime;

    // 生成速度分量数据
    const velocityData = [];
    const timeStep = flightTime / 50;

    for (let i = 0; i <= 50; i++) {
      const t = i * timeStep;
      const vx = v0 * Math.cos(theta);
      const vy = v0 * Math.sin(theta) - g * t;
      const vTotal = Math.sqrt(vx * vx + vy * vy);

      velocityData.push({
        time: Math.round(t * 100) / 100,
        vx: Math.round(vx * 10) / 10,
        vy: Math.round(vy * 10) / 10,
        vTotal: Math.round(vTotal * 10) / 10
      });
    }

    const option = {
      title: {
        text: \`速度分量变化 (v₀=\${v0}m/s, θ=\${this.data.angle}°)\`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{a}: {c}m/s'
      },
      legend: {
        data: ['水平速度', '垂直速度', '总速度'],
        bottom: 10
      },
      grid: {
        left: '12%',
        right: '8%',
        bottom: '15%',
        top: '15%'
      },
      xAxis: {
        type: 'value',
        name: '时间 (s)',
        nameLocation: 'center',
        nameGap: 30
      },
      yAxis: {
        type: 'value',
        name: '速度 (m/s)',
        nameLocation: 'center',
        nameGap: 50
      },
      series: [
        {
          name: '水平速度',
          type: 'line',
          data: velocityData.map(item => [item.time, item.vx]),
          lineStyle: { color: '#2ecc71' },
          smooth: true
        },
        {
          name: '垂直速度',
          type: 'line',
          data: velocityData.map(item => [item.time, item.vy]),
          lineStyle: { color: '#e74c3c' },
          smooth: true
        },
        {
          name: '总速度',
          type: 'line',
          data: velocityData.map(item => [item.time, item.vTotal]),
          lineStyle: { color: '#9b59b6' },
          smooth: true
        }
      ]
    };

    try {
      JSON.stringify(option);
      this.velocityChart.setOption(option);
    } catch (e) {
      console.error('速度图表配置序列化失败:', e);
    }
  },

  // 修复9：正确的生命周期管理
  onUnload() {
    if (this.trajectoryChart) {
      this.trajectoryChart.destroy();
      this.trajectoryChart = null;
    }
    if (this.velocityChart) {
      this.velocityChart.destroy();
      this.velocityChart = null;
    }
  }
});
`;

export const fixedTTML = `
<!-- 确保Canvas名称唯一 -->
<view class="chart-container">
  <lightcharts-canvas 
    canvasName="trajectory-chart" 
    bindinitchart="initTrajectoryChart" 
    style="width: 100%; height: 400px;"/>
</view>

<view class="chart-container">
  <lightcharts-canvas 
    canvasName="velocity-chart" 
    bindinitchart="initVelocityChart" 
    style="width: 100%; height: 400px;"/>
</view>
`;