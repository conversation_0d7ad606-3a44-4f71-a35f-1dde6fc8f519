export const LYNX_COMPONENTS = `🎯 LYNX COMPONENTS RULES (SOURCE-CODE VERIFIED)

=== R1: COMPONENT RESTRICTIONS (源码: lynx-components/ComponentValidator.ts) ===
RULE: 禁用HTML标签 div,p,img,span,label,button,section,article,ul,ol,li,table
RULE: 必用Lynx组件 view,text,image,scroll-view,input,textarea,picker,canvas,swiper
RULE: 移动端特有组件需注意平台兼容性
RULE: 非Lynx组件必须封装后使用

=== R2: TEXT WRAPPING RULES (源码: lynx-text/TextWrapper.ts) ===
🚨 CRITICAL: 文字和图标强制包裹规则 (重要技术约束)
RULE: 所有文字和图标必须使用 text 标签包裹
RULE: 禁止在 view 或其他容器中放置裸露文字
RULE: 严禁使用 Emoji 字符，只能使用 Font Awesome 图标
RULE: text 标签内容必须XML实体转义 &lt; &gt; &amp; &quot; &apos;

❌ 错误示例 (需要避免):
<view>这是裸露的文字</view>  <!-- 错误：文字没有包裹 -->
<view>🔥</view>  <!-- 错误：使用了Emoji -->

✅ 正确示例:
<view><text>这是正确包裹的文字</text></view>
<view><text>&#xf015;</text></view>  <!-- 正确：Font Awesome图标 -->

=== R3: ICON POSITIONING RULES (源码: lynx-icons/IconPositioner.ts) ===
RULE: 推荐直接使用纯图标，避免背景色块
RULE: 图标居中必须使用 text-align: center
RULE: 完整居中方案 display: flex + align-items: center + justify-content: center
RULE: 避免图标与背景色块组合的定位偏移问题

=== R4: SCROLL VIEW RULES (源码: lynx-scroll/ScrollViewManager.ts) ===
RULE: 所有滚动内容必须使用 scroll-view，禁止 view 滚动
RULE: scroll-view 必须设置 height 或 max-height，禁止 min-height
RULE: 列表渲染、长内容、聊天界面、文章阅读必须使用 scroll-view
RULE: 垂直滚动设置 scroll-y="true"，水平滚动设置 scroll-x="true"

=== R5: TEXT CENTERING RULES (源码: lynx-text/TextCentering.ts) ===
RULE: 文本居中必须同时使用 text-align: center + line-height = height
RULE: 禁止仅依赖 flex 布局居中 text 元素内容
RULE: 数字序号、标题、按钮文字必须严格居中
RULE: 违反居中规则属于严重样式错误

=== R6: BASIC COMPONENTS (源码: lynx-components/BasicComponents.ts) ===
RULE: text - 唯一文字容器，支持换行和样式
RULE: image - 图片组件，自闭合标签，替代HTML img
RULE: video,audio,camera,live-player,live-pusher - 媒体组件
RULE: map - 地图容器，canvas - 2D绘图画布

=== R7: ADVANCED COMPONENTS (源码: lynx-components/AdvancedComponents.ts) ===
RULE: view - 万能容器，支持 flexbox、定位、动画
RULE: scroll-view - 高性能滚动，支持下拉刷新、上拉加载
RULE: swiper - 轮播容器，支持指示器、自动播放、循环
RULE: cover-view - 原生组件覆盖层，解决层级问题

=== R8: CARD STRUCTURE RULES (源码: lynx-cards/CardStructure.ts) ===
RULE: 卡片最外层必须使用 scroll-view 包裹，禁止 view
RULE: scroll-view 必须设置 height + max-height + scroll-y="true"
RULE: 高度建议 简单400-600rpx，复杂600-800rpx，数据密集800-1000rpx
RULE: 标准结构 <scroll-view><view class="card-content">内容</view></scroll-view>

=== R9: INTERACTION COMPONENTS (源码: lynx-interaction/InteractionComponents.ts) ===
RULE: movable-view,movable-area - 拖拽移动容器
RULE: functional-page-navigator - 功能页面导航器
RULE: navigator - 声明式导航，支持多种跳转方式
RULE: 交互组件必须提供视觉反馈和状态管理

=== R10: FORM COMPONENTS (源码: lynx-forms/FormComponents.ts) ===
RULE: form,input,textarea,button - 基础表单控件
RULE: checkbox-group,radio-group,picker,slider,switch - 选择控件
RULE: picker 支持 selector,multiSelector,time,date,region 类型
RULE: 表单验证必须在客户端和服务端双重验证

=== R11: MEDIA COMPONENTS (源码: lynx-media/MediaComponents.ts) ===
RULE: image - 图片组件，支持懒加载、错误处理、多种模式
RULE: video,audio - 播放器组件，支持全屏、控制条、进度
RULE: camera,live-player,live-pusher - 实时媒体组件
RULE: 媒体组件必须处理加载失败和网络异常

=== R12: DISPLAY COMPONENTS (源码: lynx-display/DisplayComponents.ts) ===
RULE: rich-text - 富文本渲染，支持HTML子集
RULE: progress - 进度指示器，支持环形、线性样式
RULE: web-view - H5页面容器，支持与小程序通信
RULE: open-data,ad - 开放能力组件，展示用户信息和广告

=== R13: CANVAS USAGE RULES (源码: lynx-canvas/CanvasManager.ts) ===
RULE: Canvas 必须用于流程图、架构图、组织结构图等复杂图形
RULE: 每个页面应包含至少1个Canvas绘制的自定义图形
RULE: 优先使用Canvas创建动态、交互式的可视化内容
RULE: 禁止使用静态图片代替可绘制的图形

=== R14: 🚨 CANVAS INITIALIZATION RULES (源码: lynx-canvas/InitializationValidator.ts) ===
🚨 CRITICAL: Canvas初始化关键步骤防范
RULE: 步骤1 lynx.krypton.createCanvasNG() - 无参数创建Canvas Element
RULE: 步骤2 addEventListener('resize') - resize事件监听必须在绑定前设置
RULE: 步骤3 SystemInfo.pixelRatio - 高分屏适配处理
RULE: 步骤4 attachToCanvasView(name) - 绑定到Canvas View

❌ 禁止的错误模式：
RULE: 禁止 lynx.createCanvasNG("canvasName") - 不能传参数
RULE: 禁止 直接使用canvas.width/height - 没有resize监听
RULE: 禁止 忘记attachToCanvasView - Canvas不显示
RULE: 禁止 缺少pixelRatio适配 - 高分屏模糊

=== R15: CANVAS DRAWING RULES (源码: lynx-canvas/DrawingEngine.ts) ===
RULE: 基础绘图 moveTo,lineTo,rect,arc,fill,stroke
RULE: 高级特性 渐变填充,图像处理,文字渲染,阴影效果
RULE: 性能优化 离屏渲染,局部刷新,对象池,分层渲染
RULE: 交互处理 事件监听,碰撞检测,动画循环

=== R15: DATA BINDING RULES (源码: lynx-data/DataBinding.ts) ===
RULE: 所有数据绑定必须使用可选链 {{data?.property?.value}}
RULE: 数组访问必须使用可选链 {{list?.[0]?.title}}
RULE: 条件判断必须使用可选链 tt:if="{{data?.status?.loading}}"
RULE: 事件绑定必须使用可选链 bindtap="handleTap" data-id="{{item?.id}}"

=== R16: LAYOUT PATTERNS (源码: lynx-layout/LayoutPatterns.ts) ===
RULE: 响应式布局使用 view + flex 样式实现弹性布局
RULE: Grid 替代用 flex + wrap 模拟 grid 效果
RULE: 流式布局结合 scroll-view 实现长列表优化
RULE: tt:for 列表渲染必须包裹在 scroll-view 中

=== R17: COMPONENT CONSTRAINTS (源码: lynx-constraints/ComponentConstraints.ts) ===
RULE: 自闭合标签 image,input,progress,icon,checkbox,radio 必须自闭合
RULE: 容器嵌套 text 内部不能嵌套其他组件
RULE: 组件关系 swiper 必须配合 swiper-item 使用
RULE: 样式继承 大部分样式不会自动继承，需显式设置

=== MANDATORY IMPLEMENTATION RULES ===
RULE #1: 组件限制 - 禁用HTML标签+必用Lynx组件+文字包裹text
RULE #2: 滚动容器 - scroll-view包裹+height设置+scroll-y配置
RULE #3: 数据绑定 - 可选链访问+XML转义+事件绑定安全
RULE #4: Canvas初始化 - 4步骤流程+resize监听+pixelRatio适配+attachToCanvasView
RULE #5: Canvas绘图 - 复杂图形+交互式内容+性能优化
RULE #6: 组件约束 - 自闭合标签+嵌套规则+样式继承

THESE RULES ARE MANDATORY FOR FUNCTIONAL LYNX IMPLEMENTATION
`;
