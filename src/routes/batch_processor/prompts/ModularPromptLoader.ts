/**
 * Modular Prompt Loader - 统一提示词系统
 * 融合三阶段思考、技术约束、痛苦提示词的完整prompt工程解决方案
 */

import { LYNX_FRAMEWORK_CORE } from './LynxFrameworkCore';
import { LYNX_COMPONENTS } from './LynxComponents';
import { LYNX_STYLE_SYSTEM } from './LynxStyleSystem';
import { LYNX_UTILS_SYSTEM } from './LynxUtilsSystem';
import { FONT_AWESOME } from './FontAwesome';
import { BEST_PRACTICES } from './BestPractices';
import { THREAD_SYNCHRONIZATION } from './ThreadSynchronization';
import { TTML_STRICT_CONSTRAINTS } from './TTMLStrictConstraints';
import { TTSS_STRICT_CONSTRAINTS } from './TTSSStrictConstraints';
import { LIGHTCHART_PROMPT_CONTENT } from './LightChartPromptLoader';
import { UIGuidance } from './UIGuidance';
import { VISUALIZATION_GUIDANCE } from './VisualizationGuidance';

export class ModularPromptLoader {
  private static instance: ModularPromptLoader;
  private cache: Map<string, string> = new Map();

  private constructor() {}

  public static getInstance(): ModularPromptLoader {
    if (!ModularPromptLoader.instance) {
      ModularPromptLoader.instance = new ModularPromptLoader();
    }
    return ModularPromptLoader.instance;
  }

  /**
   * 构建痛苦提示词 - 强化约束执行
   */
  private buildPainPrompts(): string {
    return `
🚨 **痛苦提示词 - 严重后果警告** (PAIN PROMPTS FOR CONSTRAINT ENFORCEMENT)

**违反约束的严重后果**:
❌ 使用HTML标签(div/span/button) → 代码完全无法运行，用户体验崩溃
❌ 忘记scroll-view包裹 → 内容溢出，用户无法查看完整信息
❌ Canvas与LightChart混用 → 致命错误，应用直接崩溃
❌ 缺少可选链操作符 → 数据访问异常，功能完全失效
❌ 事件绑定错误 → 交互完全失效，用户无法操作
❌ 输出中间思考过程 → 违反三阶段协议，输出格式错误

**专业声誉损害**:
- 低级错误暴露技术理解不足
- 用户对AI代码生成能力失去信任
- 开发效率严重下降，调试时间成倍增加

**成功的奖励**:
✅ 完美遵循约束 → 代码一次性运行成功
✅ 三阶段深度思考 → 输出卓越的用户体验
✅ 专业级代码质量 → 获得用户高度认可

⚠️ **最终警告**: 任何约束违反都将导致代码完全失败，必须100%严格执行所有规则！
`;
  }

  /**
   * 构建结构化执行协议
   */
  private buildStructuredProtocol(): string {
    return `
🧠 **结构化三阶段执行协议** (STRUCTURED THINKING MANDATORY)

**执行流程**:

阶段1(内部) → 需求分析 + 创意构思 + 技术评估
阶段2(内部) → 架构设计 + 方案选择 + 性能优化  
阶段3(输出) → 直接生成完整Lynx代码


**严格约束**:
- 阶段1-2: 绝对禁止任何输出
- 阶段3: 只输出<FILES>格式代码
- 思考深度: 每阶段至少3个维度分析
- 质量标准: 生产级代码质量

🔒 **技术决策矩阵**:
- UI界面 → View+TTSS (性能+兼容)
- 数据图表 → Canvas+LightChart (交互+视觉)
- 复杂应用 → 混合架构 (优势最大化)

⚡ **执行触发**: 立即启动三阶段协议，直接输出代码！
`;
  }

  /**
   * 构建完整技术规范 - 高度结构化
   */
  private buildTechnicalSpecs(): string {
    const coreModules = [
      { content: UIGuidance, priority: 'HIGH' },
      { content: LYNX_COMPONENTS, priority: 'CRITICAL' },
      { content: LYNX_STYLE_SYSTEM, priority: 'CRITICAL' },
      { content: TTML_STRICT_CONSTRAINTS, priority: 'CRITICAL' },
      { content: TTSS_STRICT_CONSTRAINTS, priority: 'CRITICAL' },
      { content: BEST_PRACTICES, priority: 'HIGH' },
      { content: LYNX_UTILS_SYSTEM, priority: 'MEDIUM' },
      { content: LIGHTCHART_PROMPT_CONTENT, priority: 'MEDIUM' },
      { content: FONT_AWESOME, priority: 'LOW' },
      { content: THREAD_SYNCHRONIZATION, priority: 'LOW' },
      { content: VISUALIZATION_GUIDANCE, priority: 'MEDIUM' },
    ];

    return coreModules
      .sort((a, b) => {
        const priorityOrder = { CRITICAL: 0, HIGH: 1, MEDIUM: 2, LOW: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      })
      .map(module => module.content)
      .join('\n\n');
  }

  /**
   * 唯一的主方法 - 生成完整优化提示词
   */
  public getMasterLevelLynxPromptContent(): string {
    const cacheKey = 'ultimate_prompt';

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      const painPrompts = this.buildPainPrompts();
      const structuredProtocol = this.buildStructuredProtocol();
      const technicalSpecs = this.buildTechnicalSpecs();

      const ultimatePrompt = `${LYNX_FRAMEWORK_CORE}

${painPrompts}

${structuredProtocol}

## 📚 完整技术规范体系

${technicalSpecs}

## 🚀 最终执行指令

现在立即执行结构化三阶段协议：

1. **深度分析**(内部): 理解需求+评估复杂度+确定方案
2. **精细设计**(内部): 架构规划+性能优化+用户体验
3. **代码输出**(执行): 直接生成<FILES>格式的完整Lynx代码

⚠️ **绝对要求**:
- 前两阶段零输出
- 第三阶段直接输出代码
- 100%遵循所有约束
- 达到生产级质量

开始执行！`;

      this.cache.set(cacheKey, ultimatePrompt);
      return ultimatePrompt;
    } catch (error) {
      console.error('Ultimate prompt generation failed:', error);
      return this.buildFallback();
    }
  }

  private buildFallback(): string {
    return `${LYNX_FRAMEWORK_CORE}

🚨 CRITICAL: 你是Lynx框架专家，必须生成完整可运行的代码

三阶段执行:
1. 内部分析(不输出)
2. 内部设计(不输出)  
3. 代码输出(必须)

严格约束:
- 禁用HTML标签，只用Lynx组件
- scroll-view包裹可滚动内容
- 使用可选链操作符
- 正确绑定事件处理器

立即开始！`;
  }

  public clearCache(): void {
    this.cache.clear();
  }
}

// 简化导出 - 只保留核心功能
export function getMasterLevelLynxPromptContent(): string {
  return ModularPromptLoader.getInstance().getMasterLevelLynxPromptContent();
}

export default ModularPromptLoader.getInstance();
