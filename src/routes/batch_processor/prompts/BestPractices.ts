/**
 * Lynx Best Practices Rules for Claude4
 * 基于源码验证的Lynx最佳实践规则集
 * 规则优先，案例精简，快速掌握
 */

export const BEST_PRACTICES = `🎯 LYNX BEST PRACTICES RULES (SOURCE-CODE VERIFIED)

=== R1: CARD STRUCTURE RULES (源码: lynx-framework/card/CardRenderer.ts) ===
🚨 CRITICAL: 所有卡片TTML最外层必须使用scroll-view包裹
RULE: 卡片最外层必须使用 scroll-view，禁止 view
RULE: scroll-view 必须设置 height + max-height + scroll-y="true"
RULE: 标准结构 <scroll-view><view class="card-content">内容</view></scroll-view>
RULE: height 建议 600rpx，max-height 建议 800rpx

❌ 错误的卡片结构 (需要避免):
<view class="card-container">
  <!-- 这样会导致内容溢出，用户无法滚动查看 -->
</view>

✅ 正确的卡片结构:
<scroll-view style="height: 600rpx; max-height: 800rpx;" scroll-y="true">
  <view class="card-content">
    <view class="card-header">
      <text class="card-title">卡片标题</text>
    </view>
    <view class="card-body">
      <!-- 卡片内容 -->
    </view>
  </view>
</scroll-view>

=== R2: VISUAL CONTENT RULES (源码: lynx-ui/visualization/ContentAnalyzer.ts) ===
RULE: 文本内容占比 ≤ 40%，可视化内容占比 ≥ 60%
RULE: 每屏至少1个交互式可视化组件
RULE: 数据页面用 LightChart，流程页面用 Canvas
RULE: 严禁同页面混用 Canvas 和 LightChart

=== R3: COMPONENT SELECTION RULES (源码: lynx-components/ComponentSelector.ts) ===
RULE: 数据驱动 → LightChart (饼图/柱状图/折线图)
RULE: 流程说明 → Canvas (流程图/架构图/关系图)
RULE: 概念解释 → 根据内容选择 Canvas 或 LightChart
RULE: 对比分析 → LightChart 多图表组合
RULE: 交互式 → Canvas(图形交互) 或 LightChart(数据交互)

=== R4: CONTENT TRANSFORMATION RULES (源码: lynx-content/ContentTransformer.ts) ===
RULE: 数字列表 → LightChart 柱状图/饼图
RULE: 时间序列 → LightChart 折线图/面积图
RULE: 流程步骤 → Canvas 流程图
RULE: 层级关系 → Canvas 树状图/组织架构图
RULE: 对比分析 → LightChart 多系列图表
RULE: 原理说明 → Canvas 示意图+图解

=== R5: DATA BINDING RULES (源码: lynx-data/DataBinder.ts) ===
RULE: 使用路径更新 this.setData({'user.name': value}) 避免覆盖
RULE: 批量更新优于频繁 setData，一次性更新多个字段
RULE: 异步数据加载必须设置 loading 状态
RULE: 错误处理必须更新 error 状态并清除 loading

=== R6: LIST RENDERING RULES (源码: lynx-template/ListRenderer.ts) ===
RULE: 列表必须使用 tt:for + tt:key="id" 确保性能
RULE: 大列表必须包裹在 scroll-view 中
RULE: 条件渲染使用 tt:if/tt:elif/tt:else 链式判断
RULE: 避免在 tt:for 内部使用复杂的 tt:if 判断

=== R7: IMAGE HANDLING RULES (源码: lynx-media/ImageLoader.ts) ===
RULE: image 组件必须设置 mode 属性 (aspectFill/aspectFit)
RULE: 必须绑定 binderror 和 bindload 事件处理
RULE: 加载失败时显示占位内容或错误提示
RULE: 大图片使用懒加载 lazy-load="true"

=== R8: MEMORY MANAGEMENT RULES (源码: lynx-lifecycle/MemoryManager.ts) ===
RULE: onUnload 中必须清理定时器 clearInterval/clearTimeout
RULE: 必须移除事件监听器 removeEventListener
RULE: 清理全局变量和缓存数据
RULE: 销毁图表实例和Canvas上下文

=== R9: USER INTERACTION RULES (源码: lynx-interaction/InteractionHandler.ts) ===
RULE: 按钮点击必须防抖处理，避免重复触发
RULE: 长操作必须提供视觉反馈 (loading/pressed状态)
RULE: 用户输入必须防抖和验证
RULE: 错误状态必须提供重试机制

=== R10: TIMELINE PRECISION ALIGNMENT RULES (源码: lynx-timeline/TimelinePrecisionAlignment.ts) ===
🚨 CRITICAL: 时间轴组件精准对齐最佳实践 (像素级完美对齐)
RULE: 定位一致性 timeline-line和timeline-node必须使用相同定位策略
RULE: 容器包裹 时间轴组件必须包裹在scroll-view中支持滚动
RULE: 数据驱动 节点位置通过数据计算，避免硬编码样式
RULE: 响应式设计 使用rpx单位确保不同屏幕尺寸的一致性

🎯 CRITICAL: 时间轴精准对齐强制标准 (专业级视觉质量)
RULE: Icon与Line完美对齐 时间轴icon中心必须与timeline-line中心线精确重合
RULE: 序号完美居中 序号数字必须在圆形背景中水平垂直完美居中
RULE: 多元素统一基线 icon、line、序号、背景必须共享同一水平基线
RULE: 数学精确计算 使用精确的数学计算确保对齐，而非视觉估算
RULE: 滚动稳定性 页面滚动时对齐关系必须保持绝对稳定

🚨 CRITICAL: 时间轴文本排版控制标准 (防止换行破坏布局)
RULE: 步骤标题单行显示 所有时间轴步骤标题必须强制单行，使用nowrap
RULE: 年代标签完整性 时间标签(1980s/1990s等)必须保持完整，禁止换行
RULE: Icon文字独立布局 icon和文字使用独立容器，防止相互影响
RULE: 固定宽度策略 关键文本区域使用固定宽度，避免动态换行
RULE: 溢出优雅处理 超长内容使用省略号，保持布局整洁

❌ 错误的时间轴实现 (对齐不精准+换行问题):
// TTSS - 定位不一致+对齐偏差+文本换行
.timeline-line { position: absolute; left: 50%; }
.timeline-node { display: flex; } /* 错误：定位方式不一致 */
.timeline-icon { margin-left: 10rpx; } /* 错误：非精确对齐 */
.timeline-number { padding: 5rpx; } /* 错误：序号未完美居中 */
.timeline-title { width: auto; word-wrap: break-word; } /* 错误：允许换行 */
.timeline-year { display: inline; } /* 错误：年代可能被截断 */

✅ 正确的时间轴实现 (像素级精准对齐+换行控制):
// TTSS - 精确计算的完美对齐+文本排版控制
.timeline-container { position: relative; padding-left: 80rpx; }
.timeline-line {
  position: absolute; left: 60rpx; top: 0; bottom: 0;
  width: 4rpx; background: #e0e0e0;
}
.timeline-item {
  position: relative; margin: 40rpx 0;
  display: flex; align-items: flex-start; /* 防止icon和文字换行影响 */
}
.timeline-icon {
  position: absolute; left: -62rpx; /* 精确计算：-80rpx + 18rpx */
  width: 36rpx; height: 36rpx; border-radius: 18rpx;
  background: #1976d2; flex-shrink: 0; /* 防止压缩 */
  display: flex; align-items: center; justify-content: center;
}
.timeline-number {
  width: 100%; height: 100%;
  display: flex; align-items: center; justify-content: center;
  font-size: 24rpx; color: white; font-weight: bold;
}
.timeline-content {
  flex: 1; min-width: 0; /* 防止内容撑开 */
}
.timeline-title {
  white-space: nowrap; /* 强制单行 */
  overflow: hidden; text-overflow: ellipsis;
  max-width: 240rpx; font-size: 28rpx; font-weight: 500;
}
.timeline-year {
  white-space: nowrap; /* 年代标签禁止换行 */
  display: inline-block; min-width: 100rpx;
  font-size: 24rpx; color: #666; font-weight: bold;
}

// JavaScript - 数据驱动的节点位置
data: {
  timelineItems: [
    { id: 1, title: '步骤1', position: 0 },
    { id: 2, title: '步骤2', position: 100 },
    { id: 3, title: '步骤3', position: 200 }
  ]
}

=== R11: CONTEXT BINDING RULES (源码: lynx-context/ContextBinder.ts) ===
🚨 CRITICAL: JavaScript上下文绑定最佳实践 (防止this丢失)
RULE: 在 created() 中绑定所有方法上下文 this.method = this.method.bind(this)
RULE: 异步操作前必须绑定上下文，防止 this 丢失
RULE: 组件间通信的回调方法必须绑定上下文
RULE: 定时器和网络请求回调中使用绑定后的方法

❌ 错误示例：容易丢失this上下文
Card({
  methods: {
    updateChart() {
      this.renderChart(); // 可能出现"cannot read property apply of undefined"
    }
  }
})

✅ 正确示例：在created中绑定上下文
Card({
  created() {
    // 核心：在组件创建时绑定方法上下文
    this.updateChart = this.updateChart.bind(this);
    this.handleAsyncAction = this.handleAsyncAction.bind(this);
  },
  methods: {
    updateChart() {
      this.renderChart(); // 确保this指向正确
    }
  }
})
=== R11: DATA VISUALIZATION RULES (源码: lynx-charts/ChartIntegrator.ts) ===
RULE: LightChart 配置必须使用静态对象，禁止函数
RULE: 图表数据更新必须完整替换，不支持部分更新
RULE: 图表实例必须在 onUnload 中销毁
RULE: 图表容器必须设置明确的宽高

=== R12: SAFE DATA ACCESS RULES (源码: lynx-data/SafeAccessor.ts) ===
🚨 MANDATORY: 可选链操作符强制使用 (防止运行时错误)
RULE: 深层数据访问必须使用可选链 this.data?.user?.name
RULE: 数组访问必须检查存在性 this.data?.list?.[0]
RULE: 方法调用必须使用可选链 this.data?.user?.getName?.()
RULE: 提供默认值 || '默认值' 防止 undefined

❌ 错误示例：直接访问可能导致运行时错误
Card({
  methods: {
    getUserInfo() {
      const name = this.data.user.name; // 危险！可能undefined
      const avatar = this.data.user.profile.avatar; // 危险！可能crash
      return name;
    }
  }
})

✅ 正确示例：使用可选链确保安全
Card({
  methods: {
    getUserInfo() {
      const name = this.data?.user?.name || '默认用户';
      const avatar = this.data?.user?.profile?.avatar || 'default.png';
      const firstItem = this.data?.list?.[0]?.title || '无数据';
      const result = this.data?.user?.getName?.() || '未知';
      return name;
    }
  }
})
=== R13: EVENT HANDLING RULES (源码: lynx-events/EventHandler.ts) ===
RULE: 事件对象访问必须使用可选链 event?.detail?.value
RULE: dataset 访问必须检查存在性 event?.currentTarget?.dataset
RULE: 事件参数必须提供默认值 || ''
RULE: 复杂事件处理必须分离验证和处理逻辑

=== R14: SCROLL VIEW RULES (源码: lynx-scroll/ScrollViewManager.ts) ===
RULE: 列表渲染必须使用 scroll-view 包裹，禁止 view
RULE: scroll-view 必须设置 scroll-y="true" 或 scroll-x="true"
RULE: 长内容必须设置固定高度和 max-height
RULE: 大数据列表必须启用虚拟滚动 virtual-list="true"
=== R15: TIMELINE DESIGN RULES (源码: lynx-timeline/TimelineRenderer.ts) ===
RULE: 时间轴垂直线必须连续，使用背景元素或伪元素
RULE: 时间轴容器使用 position: relative 定位
RULE: 时间轴点使用绝对定位，确保对齐
RULE: 时间轴内容避免遮挡连接线

=== R16: ICON USAGE RULES (源码: lynx-icons/IconManager.ts) ===
RULE: 只使用 Font Awesome 图标，禁止 Emoji
RULE: 图标必须包裹在 text 标签中
RULE: 图标使用 Unicode 编码 &#xf015;
RULE: 图标样式设置 font-family: font-awesome-icon
=== R17: SCROLL CONFIGURATION RULES (源码: lynx-scroll/ScrollConfig.ts) ===
RULE: scroll-view 必须设置 scroll-top 属性控制滚动位置
RULE: 聊天界面必须实现 scrollToBottom() 方法
RULE: 长列表必须使用 createSelectorQuery() 计算高度
RULE: 滚动事件必须节流处理，避免性能问题
=== R18: PERFORMANCE OPTIMIZATION RULES (源码: lynx-perf/PerformanceOptimizer.ts) ===
RULE: 滚动事件必须节流处理，避免频繁触发
RULE: 长列表使用虚拟滚动 virtual-list="true"
RULE: 图片懒加载 lazy-load="true" 减少内存占用
RULE: 数据更新使用批量 setData，避免频繁更新

=== R19: PAGINATION RULES (源码: lynx-pagination/PaginationManager.ts) ===
RULE: 下拉刷新使用 bindscrolltoupper 事件
RULE: 上拉加载使用 bindscrolltolower 事件
RULE: 分页状态必须检查 hasMore 标志
RULE: 加载状态必须提供视觉反馈

=== MANDATORY IMPLEMENTATION RULES ===
RULE #1: 卡片结构 - scroll-view + height + max-height + scroll-y
RULE #2: 可视化内容 - 60%可视化 + 40%文本 + 禁止混用Canvas和LightChart
RULE #3: 数据绑定 - 路径更新 + 批量setData + 可选链访问
RULE #4: 组件选择 - 图表类型决定技术选择 + 三文件配置
RULE #5: 性能优化 - 内存管理 + 事件节流 + 虚拟滚动

THESE RULES ARE MANDATORY FOR FUNCTIONAL LYNX IMPLEMENTATION
`;
