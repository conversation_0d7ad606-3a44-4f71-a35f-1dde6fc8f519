# 🚨 LynxChart 样式属性错误分析报告

## 📋 错误概述

基于对用户代码和 node_modules 源码的深度分析，发现了一个关键的样式属性命名错误。

## 🔍 源码级错误分析

### 用户代码中的错误
```javascript
// ❌ 错误：用户代码中使用了不存在的属性
series: [{
  type: 'line',
  name: '平均温度',
  encode: { x: 'month', y: 'temperature' },
  yAxisIndex: 0,
  lineStyle: { stroke: '#ff9800', strokeWidth: 3 },
  symbolStyle: { fill: '#ff9800' } // ← 错误：LINE图表中不存在此属性
}]
```

### 源码验证分析
**源码位置**: `node_modules/@byted/lightcharts/lib/chart/line/index.d.ts:31`

```typescript
export interface LineOption extends CoordinateSeriesOption, zoneOption {
  type: 'line';
  // ... 其他属性
  lineStyle: LineStyleOption;
  marker: MarkerOption;  // ← 正确：应该使用 marker 属性
  // ... 其他属性
}

export interface MarkerOption extends ShapeStyleOption {
  show: boolean;
  symbol: ChartSymbol;
  size: number;
  // 继承 ShapeStyleOption 的 fill, stroke 等属性
}
```

## 🚨 具体错误原因

### 1. 属性名称错误
- **问题**: 用户使用了 `symbolStyle` 属性
- **源码事实**: LINE 图表使用 `marker` 属性控制点样式
- **后果**: 样式配置被忽略，点样式不生效

### 2. 图表类型特定属性混淆
- **问题**: 不同图表类型有不同的样式属性结构
- **混淆**: 将其他图表类型的属性用在 LINE 图表上
- **后果**: 配置无效，样式不生效

### 3. 源码接口定义不匹配
- **问题**: 用户代码与源码接口定义不匹配
- **源码定义**: `marker: MarkerOption`
- **用户使用**: `symbolStyle: { fill: '#ff9800' }`

## ✅ 正确的属性结构

### LINE 图表正确配置
```javascript
// ✅ 正确：基于源码接口定义的正确配置
series: [{
  type: 'line',
  name: '平均温度',
  encode: { x: 'month', y: 'temperature' },
  yAxisIndex: 0,
  lineStyle: { 
    stroke: '#ff9800', 
    strokeWidth: 3 
  },
  marker: {  // ← 正确：使用 marker 属性
    show: true,
    fill: '#ff9800',
    size: 6,
    symbol: 'circle'
  }
}]
```

### 不同图表类型的样式属性对比

| 图表类型 | 点/形状样式属性 | 源码位置 |
|---------|----------------|----------|
| LINE | `marker: MarkerOption` | `lib/chart/line/index.d.ts:31` |
| BAR | `shapeStyle: ShapeStyleOption` | `lib/chart/bar/index.d.ts` |
| PIE | `shapeStyle: ShapeStyleOption` | `lib/chart/pie/index.d.ts` |
| SCATTER | `marker: MarkerOption` | `lib/chart/scatter/index.d.ts` |

## 🎯 源码验证的关键发现

### 1. MarkerOption 接口定义
**源码位置**: `lib/chart/line/index.d.ts:13-17`
```typescript
export interface MarkerOption extends ShapeStyleOption {
  show: boolean;    // 是否显示标记点
  symbol: ChartSymbol;  // 标记点形状
  size: number;     // 标记点大小
  // 继承 ShapeStyleOption: fill, stroke, strokeWidth 等
}
```

### 2. 继承关系分析
- `MarkerOption` 继承 `ShapeStyleOption`
- 因此可以使用 `fill`, `stroke`, `strokeWidth` 等样式属性
- 但必须在 `marker` 对象内使用，不能直接使用 `symbolStyle`

### 3. 默认值分析
**源码位置**: `lib/chart/line/index.js:124-128`
```javascript
marker: {
  show: 'auto',
  symbol: 'circle',
  size: 8,
  fill: '#ffffff',
  stroke: null,
  lineWidth: 2,
}
```

## 📊 错误影响分析

### 1. 样式不生效
- **现象**: 点样式配置被忽略
- **原因**: 使用了不存在的属性名
- **影响**: 图表显示不符合预期

### 2. 配置被静默忽略
- **现象**: 没有报错，但配置无效
- **原因**: LightChart 忽略未知属性
- **影响**: 调试困难，不易发现问题

### 3. 开发效率降低
- **现象**: 样式调试时间增加
- **原因**: 属性名错误导致反复尝试
- **影响**: 开发体验差

## 🔥 关键规则总结

### 图表类型特定样式属性规则

1. **LINE 图表**:
   - 线条样式: `lineStyle: { stroke, strokeWidth }`
   - 点样式: `marker: { show, fill, size, symbol }`

2. **BAR 图表**:
   - 柱子样式: `shapeStyle: { fill, stroke, strokeWidth }`

3. **PIE 图表**:
   - 扇形样式: `shapeStyle: { fill, stroke, strokeWidth }`

4. **通用规则**:
   - 基于源码接口定义验证属性名
   - 不同图表类型使用对应的样式属性结构
   - 避免混用不同图表类型的属性名

## 📈 最佳实践建议

### 1. 源码接口验证
- 查看对应图表类型的接口定义
- 确认属性名和结构的正确性
- 避免使用不存在的属性

### 2. 类型特定配置
- 了解不同图表类型的特定属性
- 使用正确的样式属性名称
- 遵循源码定义的接口结构

### 3. 调试策略
- 检查样式不生效时首先验证属性名
- 对比源码接口定义
- 使用正确的属性结构

## 📊 总结

**根本原因**: 用户使用了 `symbolStyle` 属性，但 LINE 图表应该使用 `marker` 属性

**源码验证**: `lib/chart/line/index.d.ts:31` 明确定义了 `marker: MarkerOption`

**解决方案**: 使用正确的属性名称和结构，基于源码接口定义进行配置

**关键规则**: 不同图表类型有不同的样式属性名称，必须基于源码接口定义使用正确的属性

这个错误分析已经添加到 `LightChartPromptLoader.ts` 的 R63-R64 规则中，确保 Claude4 能够正确使用图表类型特定的样式属性。
