<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FontAwesome 图标过滤器使用示例</title>
    <style>
        @font-face {
            font-family: 'font-awesome-icon';
            src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-solid-900.woff2') format('woff2');
            font-weight: normal;
            font-style: normal;
        }
        
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        
        .icon {
            font-family: 'font-awesome-icon';
            font-size: 24px;
            margin-bottom: 5px;
            color: #333;
        }
        
        .icon-code {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        
        .section {
            margin: 30px 0;
        }
        
        .section h3 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FontAwesome 图标过滤器使用示例</h1>
        
        <div class="stats">
            <h3>过滤统计</h3>
            <p id="stats-info">正在加载...</p>
        </div>
        
        <div class="controls">
            <button type="button" class="btn btn-primary" onclick="showRecommended()">显示推荐图标</button>
            <button type="button" class="btn btn-success" onclick="showBasicIcons()">显示基础图标</button>
            <button type="button" class="btn btn-warning" onclick="showAllAvailable()">显示所有可用图标</button>
        </div>
        
        <div class="section">
            <h3>推荐使用的图标 (经过测试确认)</h3>
            <div id="recommended-icons" class="icon-grid"></div>
        </div>
        
        <div class="section">
            <h3>基础UI图标 (f000-f07f 范围)</h3>
            <div id="basic-icons" class="icon-grid"></div>
        </div>
        
        <div class="section">
            <h3>所有可用图标 (f000-f1ff 过滤后)</h3>
            <div id="all-available" class="icon-grid"></div>
        </div>
    </div>

    <script>
        // 基于实际测试的禁用范围
        const DISABLED_RANGES = [
            // f0xx 范围后半段不可用
            { start: 0xf080, end: 0xf08f },
            { start: 0xf090, end: 0xf09f },
            { start: 0xf0a0, end: 0xf0af },
            { start: 0xf0b0, end: 0xf0bf },
            { start: 0xf0c8, end: 0xf0cf },
            { start: 0xf0d8, end: 0xf0df },
            { start: 0xf0e8, end: 0xf0ef },
            { start: 0xf0f8, end: 0xf0ff },
            
            // f1xx 范围大部分不可用
            { start: 0xf100, end: 0xf11f },
            { start: 0xf120, end: 0xf13f },
            { start: 0xf140, end: 0xf15f },
            { start: 0xf160, end: 0xf17f },
            { start: 0xf180, end: 0xf19f },
            { start: 0xf1a0, end: 0xf1bf },
            { start: 0xf1c1, end: 0xf1c7 }, // 保留 f1c0
            { start: 0xf1c9, end: 0xf1cf },
            { start: 0xf1d0, end: 0xf1ef },
            { start: 0xf1f1, end: 0xf1ff }, // 保留 f1f0
            
            // f2xx+ 范围大部分不可用
            { start: 0xf200, end: 0xf8ff },
        ];
        
        // 推荐的图标列表
        const RECOMMENDED_ICONS = {
            basic: [
                'f000', 'f001', 'f002', 'f003', 'f004', 'f005', 'f006', 'f007',
                'f008', 'f009', 'f00a', 'f00b', 'f00c', 'f00d', 'f00e', 'f010',
                'f011', 'f012', 'f013', 'f014', 'f015', 'f016', 'f017', 'f018',
                'f019', 'f01a', 'f01b', 'f01c', 'f01d', 'f01e'
            ],
            arrows: [
                'f060', 'f061', 'f062', 'f063', 'f064', 'f065', 'f066', 'f067',
                'f068', 'f069', 'f06a', 'f06b', 'f06c', 'f06d', 'f06e', 'f070'
            ],
            files: [
                'f016', 'f017', 'f019', 'f01c', 'f0c5', 'f0c6', 'f0c7', 'f0c9'
            ]
        };
        
        // 检查图标是否被禁用
        function isIconDisabled(codeNum) {
            return DISABLED_RANGES.some(range => 
                codeNum >= range.start && codeNum <= range.end
            );
        }
        
        // 生成可用图标列表
        function generateAvailableIcons(start, end) {
            const icons = [];
            for (let i = start; i <= end; i++) {
                if (!isIconDisabled(i)) {
                    icons.push(i.toString(16).padStart(4, '0'));
                }
            }
            return icons;
        }
        
        // 渲染图标网格
        function renderIcons(container, icons) {
            container.innerHTML = '';
            icons.forEach(code => {
                const item = document.createElement('div');
                item.className = 'icon-item';
                item.innerHTML = `
                    <div class="icon">&#x${code};</div>
                    <div class="icon-code">${code}</div>
                `;
                container.appendChild(item);
            });
        }
        
        // 显示推荐图标
        function showRecommended() {
            const container = document.getElementById('recommended-icons');
            const allRecommended = [
                ...RECOMMENDED_ICONS.basic,
                ...RECOMMENDED_ICONS.arrows,
                ...RECOMMENDED_ICONS.files
            ];
            renderIcons(container, allRecommended);
        }
        
        // 显示基础图标
        function showBasicIcons() {
            const container = document.getElementById('basic-icons');
            const basicIcons = generateAvailableIcons(0xf000, 0xf07f);
            renderIcons(container, basicIcons);
        }
        
        // 显示所有可用图标
        function showAllAvailable() {
            const container = document.getElementById('all-available');
            const allIcons = generateAvailableIcons(0xf000, 0xf1ff);
            renderIcons(container, allIcons);
        }
        
        // 更新统计信息
        function updateStats() {
            const totalRange = 0xf1ff - 0xf000 + 1;
            const availableIcons = generateAvailableIcons(0xf000, 0xf1ff);
            const disabledCount = totalRange - availableIcons.length;
            
            document.getElementById('stats-info').innerHTML = `
                <strong>f000-f1ff 范围统计：</strong><br>
                • 总图标数：${totalRange}<br>
                • 可用图标：${availableIcons.length} (${Math.round(availableIcons.length/totalRange*100)}%)<br>
                • 已禁用：${disabledCount} (${Math.round(disabledCount/totalRange*100)}%)<br>
                • 推荐使用：${Object.values(RECOMMENDED_ICONS).flat().length} 个
            `;
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            showRecommended();
            showBasicIcons();
        });
    </script>
</body>
</html>
