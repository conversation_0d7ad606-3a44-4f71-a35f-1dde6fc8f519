<scroll-view scroll-y="true" style="width: 100%; max-height: 100vh;">
  <view class="container">
    <!-- 头部标题 -->
    <view class="header">
      <text class="main-title">Font Awesome 图标渲染测试</text>
      <text class="subtitle">验证Font Awesome 6.7.2图标在Lynx框架中的显示效果</text>
    </view>

    <!-- 你的TTML代码中使用的图标测试 -->
    <view class="card">
      <view class="card-header">
        <text class="card-title">原始TTML代码中的图标</text>
      </view>
      <view class="icon-grid">
        <view class="icon-item">
          <text class="icon">&#xf080;</text>
          <text class="icon-code">&#xf080;</text>
          <text class="icon-name">chart-bar (第一个图标)</text>
          <text class="status-good">应该正常显示</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf0eb;</text>
          <text class="icon-code">&#xf0eb;</text>
          <text class="icon-name">lightbulb (第二个图标)</text>
          <text class="status-warning">需要验证</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf1c0;</text>
          <text class="icon-code">&#xf1c0;</text>
          <text class="icon-name">database</text>
          <text class="status-warning">需要验证</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf0e8;</text>
          <text class="icon-code">&#xf0e8;</text>
          <text class="icon-name">sitemap</text>
          <text class="status-warning">需要验证</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf0f6;</text>
          <text class="icon-code">&#xf0f6;</text>
          <text class="icon-name">file-text-o</text>
          <text class="status-bad">FA5过时编码</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf017;</text>
          <text class="icon-code">&#xf017;</text>
          <text class="icon-name">clock</text>
          <text class="status-good">应该正常显示</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf0ad;</text>
          <text class="icon-code">&#xf0ad;</text>
          <text class="icon-name">wrench</text>
          <text class="status-good">应该正常显示</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf005;</text>
          <text class="icon-code">&#xf005;</text>
          <text class="icon-name">star</text>
          <text class="status-good">应该正常显示</text>
        </view>
      </view>
    </view>

    <!-- 替代方案测试 -->
    <view class="card">
      <view class="card-header">
        <text class="card-title">推荐的替代方案</text>
      </view>
      <view class="icon-grid">
        <view class="icon-item">
          <text class="icon">&#xf15c;</text>
          <text class="icon-code">&#xf15c;</text>
          <text class="icon-name">file-text</text>
          <text class="status-good">替代f0f6</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf0ce;</text>
          <text class="icon-code">&#xf0ce;</text>
          <text class="icon-name">table</text>
          <text class="status-good">替代database</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf013;</text>
          <text class="icon-code">&#xf013;</text>
          <text class="icon-name">cog</text>
          <text class="status-good">替代sitemap</text>
        </view>
      </view>
    </view>

    <!-- 已验证安全的图标 -->
    <view class="card">
      <view class="card-header">
        <text class="card-title">已验证安全的图标</text>
      </view>
      <view class="icon-grid">
        <view class="icon-item">
          <text class="icon">&#xf015;</text>
          <text class="icon-code">&#xf015;</text>
          <text class="icon-name">home</text>
          <text class="status-good">安全</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf007;</text>
          <text class="icon-code">&#xf007;</text>
          <text class="icon-name">user</text>
          <text class="status-good">安全</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf073;</text>
          <text class="icon-code">&#xf073;</text>
          <text class="icon-name">calendar</text>
          <text class="status-good">安全</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf07b;</text>
          <text class="icon-code">&#xf07b;</text>
          <text class="icon-name">folder</text>
          <text class="status-good">安全</text>
        </view>
        
        <view class="icon-item">
          <text class="icon">&#xf002;</text>
          <text class="icon-code">&#xf002;</text>
          <text class="icon-name">search</text>
          <text class="status-good">安全</text>
        </view>
      </view>
    </view>

    <!-- 测试说明 -->
    <view class="card">
      <view class="card-header">
        <text class="card-title">测试说明</text>
      </view>
      <view class="test-info">
        <text class="info-text">1. 如果图标显示为方块或问号，说明该Unicode编码在当前TTF文件中不存在</text>
        <text class="info-text">2. 绿色标记的图标已经过验证，可以安全使用</text>
        <text class="info-text">3. 黄色标记的图标需要在项目中进一步测试</text>
        <text class="info-text">4. 红色标记的图标确认不可用，需要使用替代方案</text>
        <text class="info-text">5. 建议优先使用已验证安全的图标编码</text>
      </view>
    </view>
  </view>
</scroll-view>
